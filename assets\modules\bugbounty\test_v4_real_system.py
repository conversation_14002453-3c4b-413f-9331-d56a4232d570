#!/usr/bin/env python3
"""
🔥 اختبار النظام v4 الحقيقي الشامل
استخدام /v4_website مع الصور الثلاث والتأثيرات الحقيقية
"""

import json
import time
import requests
from datetime import datetime

def test_v4_real_system():
    """اختبار النظام v4 الحقيقي"""
    print("🔥" * 60)
    print("🔥 اختبار النظام v4 الحقيقي مع التأثيرات الكاملة 🔥")
    print("🔥" * 60)
    
    base_url = "http://localhost:8000"
    
    # ثغرة للاختبار
    vulnerability = {
        "name": "XSS_Real_V4_Test",
        "type": "Cross-Site Scripting",
        "url": "https://httpbin.org/html",
        "payload": "<script>alert('🔥 V4 SYSTEM TEST! 🔥'); document.body.style.background='red';</script>"
    }
    
    # اختبار المراحل الثلاث
    stages = ['before', 'during', 'after']
    
    for i, stage in enumerate(stages):
        print(f"\n📸 [{i+1}/3] اختبار مرحلة {stage.upper()} - النظام v4")
        
        try:
            # البيانات للنظام v4
            payload = {
                "url": vulnerability["url"],
                "vulnerability_name": vulnerability["name"],
                "vulnerability_type": vulnerability["type"],
                "stage": stage,
                "payload_data": vulnerability["payload"],
                "target_parameter": "test_param",
                "filename": f"{stage}_v4_test",
                "report_id": f"v4_test_{stage}_{int(time.time())}"
            }
            
            print(f"📤 إرسال طلب مرحلة {stage} للنظام v4...")
            print(f"📋 البيانات: {json.dumps(payload, indent=2, ensure_ascii=False)}")
            
            # استدعاء النظام v4 الحقيقي
            response = requests.post(
                f"{base_url}/v4_website",
                json=payload,
                timeout=120
            )
            
            print(f"📥 رد النظام v4: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    result = response.json()
                    print(f"✅ نجح اختبار مرحلة {stage}!")
                    print(f"📊 النتيجة: {json.dumps(result, indent=2, ensure_ascii=False)}")
                    
                    # استخراج معلومات الصورة
                    screenshot_path = result.get('screenshot_path', 'غير محدد')
                    file_size = result.get('file_size', 0)
                    
                    print(f"📷 مسار الصورة: {screenshot_path}")
                    print(f"📊 حجم الملف: {file_size} bytes")
                    
                except json.JSONDecodeError:
                    print(f"❌ خطأ في تحليل JSON: {response.text[:300]}")
            else:
                print(f"❌ فشل مرحلة {stage}: {response.status_code}")
                print(f"📄 Response: {response.text[:500]}")
                
        except Exception as e:
            print(f"❌ خطأ في مرحلة {stage}: {e}")
        
        # انتظار بين المراحل
        if i < len(stages) - 1:
            print(f"⏳ انتظار 5 ثواني قبل المرحلة التالية...")
            time.sleep(5)
    
    print("\n🎉 انتهى اختبار النظام v4 الحقيقي!")

if __name__ == "__main__":
    test_v4_real_system()
