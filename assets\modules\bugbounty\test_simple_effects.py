#!/usr/bin/env python3
"""
🔥 اختبار بسيط للتأثيرات الديناميكية
اختبار مباشر للدوال بدون السيرفر
"""

import asyncio
import sys
import os
from pathlib import Path

# إضافة المسار للوحدات
sys.path.append(str(Path(__file__).parent))

from screenshot_service import ScreenshotService

async def test_simple_effects():
    """اختبار بسيط للتأثيرات"""
    print("🔥" * 50)
    print("🔥 اختبار التأثيرات الديناميكية مباشرة 🔥")
    print("🔥" * 50)
    
    service = ScreenshotService()
    
    try:
        # تهيئة الخدمة
        print("🚀 تهيئة خدمة الصور...")
        await service.initialize_playwright()
        
        # اختبار الثغرات الثلاث
        vulnerabilities = [
            {
                "name": "XSS_Test_Before",
                "type": "XSS",
                "url": "https://httpbin.org/html",
                "stage": "before",
                "payload": "alert('before test')"
            },
            {
                "name": "XSS_Test_During", 
                "type": "XSS",
                "url": "https://httpbin.org/html",
                "stage": "during",
                "payload": "alert('during test')"
            },
            {
                "name": "XSS_Test_After",
                "type": "XSS", 
                "url": "https://httpbin.org/html",
                "stage": "after",
                "payload": "alert('after test')"
            }
        ]
        
        for i, vuln in enumerate(vulnerabilities):
            print(f"\n📸 [{i+1}/3] اختبار مرحلة {vuln['stage'].upper()}")
            
            try:
                result = service.capture_vulnerability_screenshot_dynamic(
                    url=vuln["url"],
                    report_id=f"test_{int(asyncio.get_event_loop().time())}",
                    filename=f"{vuln['stage']}_test",
                    vulnerability_name=vuln["name"],
                    vulnerability_type=vuln["type"],
                    stage=vuln["stage"],
                    payload_data=vuln["payload"]
                )
                
                if result and result.get('success'):
                    screenshot_path = result.get('screenshot_path', 'غير محدد')
                    file_size = result.get('file_size', 0)
                    print(f"✅ نجح اختبار {vuln['stage']}: {screenshot_path} ({file_size} bytes)")
                    
                    # التحقق من وجود الملف
                    if os.path.exists(screenshot_path):
                        print(f"📁 الملف موجود: ✅")
                    else:
                        print(f"⚠️ الملف غير موجود")
                else:
                    error = result.get('error', 'خطأ غير محدد') if result else 'لا توجد نتيجة'
                    print(f"❌ فشل اختبار {vuln['stage']}: {error}")
                    
            except Exception as e:
                print(f"❌ خطأ في اختبار {vuln['stage']}: {e}")
            
            # انتظار بين الاختبارات
            if i < len(vulnerabilities) - 1:
                print("⏳ انتظار 3 ثواني...")
                await asyncio.sleep(3)
        
        print("\n📊 فحص الصور المُلتقطة:")
        screenshots_dir = Path("screenshots")
        if screenshots_dir.exists():
            screenshots = list(screenshots_dir.rglob("*.png"))
            print(f"📁 وجد {len(screenshots)} صورة:")
            
            for screenshot in sorted(screenshots)[-10:]:  # آخر 10 صور
                file_size = screenshot.stat().st_size if screenshot.exists() else 0
                print(f"   📷 {screenshot.name} ({file_size} bytes)")
        else:
            print("⚠️ مجلد الصور غير موجود")
            
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        # تنظيف
        try:
            await service.cleanup()
            print("🧹 تم التنظيف")
        except:
            pass
    
    print("\n🎉 انتهى الاختبار المباشر!")

if __name__ == "__main__":
    asyncio.run(test_simple_effects())
