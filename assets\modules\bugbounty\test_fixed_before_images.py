#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json
import time

def test_fixed_before_images():
    """اختبار الإصلاح لصور before"""
    
    print("🔥 اختبار الإصلاح لصور before")
    print("🎯 اختبار إنشاء صور before مع إرجاع file_path صحيح")
    print("=" * 60)
    
    test_id = f"fixed_before_test_{int(time.time())}"
    print(f"📋 Test ID: {test_id}")
    
    # اختبار إنشاء صورة before
    print("\n📸 اختبار إنشاء صورة before...")
    
    data = {
        'url': 'http://testphp.vulnweb.com/search.php',
        'filename': 'before_XSS_FIXED_TEST',
        'report_id': test_id,
        'vulnerability_name': 'XSS Cross Site Scripting',
        'vulnerability_type': 'XSS',
        'stage': 'before',
        'payload_data': '<script>alert("FIXED_TEST")</script>',
        'target_parameter': 'searchFor'
    }
    
    try:
        print("🔗 إرسال طلب إنشاء صورة before...")
        response = requests.post(
            'http://localhost:8000/v4_website',
            json=data,
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            
            print(f"✅ استجابة السيرفر: {response.status_code}")
            print(f"📊 محتوى الاستجابة:")
            
            # طباعة جميع المفاتيح في الاستجابة
            for key, value in result.items():
                if key == 'base64':
                    print(f"   {key}: [base64 data - {len(str(value))} chars]")
                elif key == 'screenshot_data':
                    print(f"   {key}: [screenshot data - {len(str(value))} chars]")
                else:
                    print(f"   {key}: {value}")
            
            # التحقق من وجود file_path
            if 'file_path' in result:
                file_path = result['file_path']
                file_size = result.get('file_size', 0)
                
                print(f"\n🎉 نجح الإصلاح!")
                print(f"   📁 file_path: {file_path}")
                print(f"   📏 file_size: {file_size:,} bytes")
                
                # التحقق من وجود الملف فعلياً
                import os
                if os.path.exists(file_path):
                    actual_size = os.path.getsize(file_path)
                    print(f"   ✅ الملف موجود فعلياً: {actual_size:,} bytes")
                    
                    if actual_size > 0:
                        print(f"   🎉 الإصلاح نجح بالكامل! صورة before تُنشأ وتُحفظ بنجاح!")
                        return True
                    else:
                        print(f"   ⚠️ الملف موجود لكن حجمه 0")
                else:
                    print(f"   ❌ الملف غير موجود في المسار المُرجع")
            else:
                print(f"\n❌ لا يوجد file_path في الاستجابة")
                print(f"   المفاتيح الموجودة: {list(result.keys())}")
        else:
            print(f"❌ خطأ HTTP: {response.status_code}")
            print(f"   الاستجابة: {response.text}")
            
    except Exception as e:
        print(f"❌ خطأ في الطلب: {e}")
        return False
    
    return False

def test_all_stages():
    """اختبار جميع المراحل"""
    
    print("\n🧪 اختبار جميع المراحل (before, during, after)...")
    
    test_id = f"all_stages_test_{int(time.time())}"
    stages = ['before', 'during', 'after']
    results = {}
    
    for stage in stages:
        print(f"\n📸 اختبار مرحلة {stage}...")
        
        data = {
            'url': 'http://testphp.vulnweb.com/search.php',
            'filename': f'{stage}_XSS_ALL_STAGES_TEST',
            'report_id': test_id,
            'vulnerability_name': 'XSS Cross Site Scripting',
            'vulnerability_type': 'XSS',
            'stage': stage,
            'payload_data': '<script>alert("ALL_STAGES_TEST")</script>',
            'target_parameter': 'searchFor'
        }
        
        try:
            response = requests.post(
                'http://localhost:8000/v4_website',
                json=data,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                
                if result.get('success') and 'file_path' in result:
                    file_path = result['file_path']
                    file_size = result.get('file_size', 0)
                    
                    print(f"   ✅ {stage}: {file_size:,} bytes")
                    results[stage] = {
                        'success': True,
                        'file_path': file_path,
                        'file_size': file_size
                    }
                else:
                    print(f"   ❌ {stage}: فشل - {result.get('error', 'Unknown')}")
                    results[stage] = {'success': False}
            else:
                print(f"   ❌ {stage}: HTTP {response.status_code}")
                results[stage] = {'success': False}
                
        except Exception as e:
            print(f"   ❌ {stage}: خطأ - {e}")
            results[stage] = {'success': False}
    
    # ملخص النتائج
    print(f"\n📊 ملخص النتائج:")
    successful_stages = [stage for stage, result in results.items() if result.get('success')]
    
    print(f"   ✅ نجحت: {len(successful_stages)}/3 مراحل")
    print(f"   📸 المراحل الناجحة: {', '.join(successful_stages)}")
    
    if len(successful_stages) == 3:
        print(f"\n🎉 جميع المراحل تعمل بشكل مثالي!")
        print(f"   الإصلاح نجح بالكامل!")
        return True
    elif len(successful_stages) > 0:
        failed_stages = [stage for stage in stages if stage not in successful_stages]
        print(f"\n⚠️ بعض المراحل تعمل، البعض الآخر يحتاج إصلاح:")
        print(f"   ❌ فشلت: {', '.join(failed_stages)}")
        return False
    else:
        print(f"\n❌ جميع المراحل فشلت - يحتاج مراجعة شاملة")
        return False

if __name__ == "__main__":
    print("🚀 بدء اختبار الإصلاحات...")
    
    # اختبار صور before
    before_success = test_fixed_before_images()
    
    # اختبار جميع المراحل
    all_stages_success = test_all_stages()
    
    print(f"\n🏁 النتائج النهائية:")
    print(f"   📸 صور before: {'✅ تعمل' if before_success else '❌ لا تعمل'}")
    print(f"   🎯 جميع المراحل: {'✅ تعمل' if all_stages_success else '❌ بعضها لا يعمل'}")
    
    if before_success and all_stages_success:
        print(f"\n🎉 الإصلاح نجح بالكامل! جميع الصور تُنشأ وتُرجع مساراتها بشكل صحيح!")
    elif before_success:
        print(f"\n✅ إصلاح صور before نجح، لكن هناك مشاكل أخرى")
    else:
        print(f"\n❌ الإصلاح لم ينجح - يحتاج مراجعة إضافية")
