#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار شامل لجميع الإصلاحات - التأثيرات البصرية لجميع الثغرات
"""

import requests
import json
import time
import os
from pathlib import Path

def test_all_vulnerability_fixes():
    """اختبار شامل لجميع إصلاحات الثغرات"""
    
    print("🔥 اختبار شامل لجميع الإصلاحات - التأثيرات البصرية لجميع الثغرات")
    print("=" * 80)
    
    # التحقق من سيرفر Python
    try:
        response = requests.get('http://localhost:8000/health', timeout=5)
        if response.status_code == 200:
            print("✅ سيرفر Python يعمل بنجاح")
        else:
            print(f"❌ سيرفر Python لا يعمل: {response.status_code}")
            return
    except Exception as e:
        print(f"❌ خطأ في الاتصال بسيرفر Python: {e}")
        return
    
    # اختبار جميع أنواع الثغرات
    vulnerabilities = [
        {
            'name': 'XSS Cross Site Scripting',
            'type': 'XSS',
            'url': 'http://testphp.vulnweb.com/search.php',
            'parameter': 'searchFor',
            'payload': '<script>alert("XSS_TEST")</script>'
        },
        {
            'name': 'SQL Injection',
            'type': 'SQL',
            'url': 'http://testphp.vulnweb.com/artists.php',
            'parameter': 'artist',
            'payload': "' OR '1'='1' --"
        },
        {
            'name': 'Command Injection',
            'type': 'Command',
            'url': 'http://testphp.vulnweb.com/search.php',
            'parameter': 'cmd',
            'payload': '; whoami'
        },
        {
            'name': 'Local File Inclusion',
            'type': 'LFI',
            'url': 'http://testphp.vulnweb.com/search.php',
            'parameter': 'file',
            'payload': '../../../../etc/passwd'
        }
    ]
    
    print(f"\n🧪 اختبار {len(vulnerabilities)} أنواع ثغرات مختلفة...")
    
    for i, vuln in enumerate(vulnerabilities, 1):
        print(f"\n{'='*60}")
        print(f"🎯 اختبار {i}/{len(vulnerabilities)}: {vuln['name']}")
        print(f"{'='*60}")
        
        test_vulnerability_visual_impact(vuln)
        
        # انتظار قصير بين الاختبارات
        time.sleep(2)
    
    print(f"\n🎉 تم الانتهاء من اختبار جميع الثغرات!")
    print("📊 تحقق من النتائج أدناه...")
    
    # تحليل النتائج النهائية
    analyze_all_results()

def test_vulnerability_visual_impact(vuln):
    """اختبار التأثيرات البصرية لثغرة واحدة"""
    
    report_id = f"fix_test_{vuln['type']}_{int(time.time())}"
    
    print(f"🔗 الموقع المستهدف: {vuln['url']}")
    print(f"🎯 نوع الثغرة: {vuln['type']}")
    print(f"📝 المعامل: {vuln['parameter']}")
    print(f"💉 Payload: {vuln['payload']}")
    
    # التقاط الصور الثلاث مع التحسينات الجديدة
    stages = ['before', 'during', 'after']
    results = {}
    
    for stage in stages:
        print(f"\n📸 التقاط صورة {stage}...")
        
        result = capture_enhanced_screenshot(
            url=vuln['url'],
            filename=f"{stage}_{vuln['type']}_FIXED",
            report_id=report_id,
            vulnerability_name=vuln['name'],
            vulnerability_type=vuln['type'],
            stage=stage,
            payload_data=vuln['payload'],
            target_parameter=vuln['parameter']
        )
        
        if result['success']:
            size = result.get('file_size', 0)
            results[stage] = size
            print(f"   ✅ نجح: {size:,} bytes")
            
            # تحليل فوري للحجم
            if stage == 'before':
                print(f"   📊 صورة الموقع الأصلي")
            elif stage == 'during':
                if size != results.get('before', 0):
                    print(f"   🔄 تغيير في المحتوى - payload مُطبق")
                else:
                    print(f"   ⚠️ لا يوجد تغيير واضح")
            elif stage == 'after':
                if size != results.get('during', 0):
                    print(f"   🎉 تأثيرات بصرية جديدة - الإصلاح يعمل!")
                else:
                    print(f"   ❌ لا توجد تأثيرات إضافية")
        else:
            print(f"   ❌ فشل: {result.get('error', 'خطأ غير محدد')}")
            results[stage] = 0
    
    # تحليل مفصل للثغرة
    analyze_vulnerability_results(vuln['type'], results)

def analyze_vulnerability_results(vuln_type, results):
    """تحليل مفصل لنتائج ثغرة واحدة"""
    
    print(f"\n📊 تحليل مفصل لثغرة {vuln_type}:")
    
    if all(stage in results for stage in ['before', 'during', 'after']):
        before = results['before']
        during = results['during']
        after = results['after']
        
        print(f"   📸 قبل الاستغلال: {before:,} bytes")
        print(f"   📸 أثناء الاستغلال: {during:,} bytes")
        print(f"   📸 بعد الاستغلال: {after:,} bytes")
        
        # تحليل التغييرات
        before_during_change = abs(during - before)
        during_after_change = abs(after - during)
        total_change = abs(after - before)
        
        print(f"\n   📈 التغييرات:")
        print(f"      قبل → أثناء: {before_during_change:,} bytes")
        print(f"      أثناء → بعد: {during_after_change:,} bytes")
        print(f"      إجمالي: {total_change:,} bytes")
        
        # تقييم الإصلاحات
        print(f"\n   🎯 تقييم الإصلاحات:")
        
        if before_during_change > 0:
            print(f"      ✅ payload يُؤثر على الصفحة")
        else:
            print(f"      ⚠️ payload لا يُؤثر على الصفحة")
            
        if during_after_change > 0:
            print(f"      🎉 الإصلاحات تعمل - تأثيرات بصرية إضافية!")
        else:
            print(f"      ❌ الإصلاحات لا تعمل - لا توجد تأثيرات إضافية")
            
        # تحديد نوع التأثير المتوقع
        expected_impact = get_expected_impact(vuln_type)
        print(f"      📋 التأثير المتوقع: {expected_impact}")
        
    else:
        print(f"   ❌ لم يتم التقاط جميع الصور بنجاح")

def get_expected_impact(vuln_type):
    """تحديد التأثير المتوقع لكل نوع ثغرة"""
    
    impacts = {
        'XSS': 'تنبيه JavaScript + تغيير كامل للصفحة',
        'SQL': 'عرض بيانات مسربة من قاعدة البيانات',
        'Command': 'عرض معلومات النظام والأوامر',
        'LFI': 'عرض محتوى ملفات النظام'
    }
    
    return impacts.get(vuln_type, 'تأثيرات بصرية حسب نوع الثغرة')

def capture_enhanced_screenshot(url, filename, report_id, vulnerability_name, vulnerability_type, stage, payload_data=None, target_parameter=None):
    """التقاط صورة مع التحسينات الجديدة"""
    
    try:
        data = {
            'url': url,
            'filename': filename,
            'report_id': report_id,
            'vulnerability_name': vulnerability_name,
            'vulnerability_type': vulnerability_type,
            'stage': stage,
            'payload_data': payload_data,
            'target_parameter': target_parameter
        }
        
        response = requests.post(
            'http://localhost:8000/v4_website',
            json=data,
            timeout=30
        )
        
        if response.status_code == 200:
            return response.json()
        else:
            return {
                'success': False,
                'error': f'HTTP {response.status_code}: {response.text[:100]}'
            }
            
    except Exception as e:
        return {
            'success': False,
            'error': str(e)
        }

def analyze_all_results():
    """تحليل شامل لجميع النتائج"""
    
    print(f"\n📊 تحليل شامل لجميع النتائج:")
    print("=" * 60)
    
    screenshots_dir = Path("screenshots")
    
    if not screenshots_dir.exists():
        print("❌ مجلد الصور غير موجود")
        return
    
    # البحث عن صور الإصلاحات
    fixed_files = list(screenshots_dir.rglob("*FIXED*.png"))
    
    if not fixed_files:
        print("❌ لم يتم العثور على صور الإصلاحات")
        return
    
    print(f"✅ تم العثور على {len(fixed_files)} صورة من الإصلاحات")
    
    # تجميع حسب نوع الثغرة
    vuln_groups = {}
    
    for file in fixed_files:
        # استخراج نوع الثغرة من اسم الملف
        for vuln_type in ['XSS', 'SQL', 'Command', 'LFI']:
            if vuln_type in file.name:
                if vuln_type not in vuln_groups:
                    vuln_groups[vuln_type] = []
                vuln_groups[vuln_type].append(file)
                break
    
    # عرض النتائج لكل نوع ثغرة
    for vuln_type, files in vuln_groups.items():
        print(f"\n🎯 {vuln_type} ({len(files)} صورة):")
        
        stages = {'before': None, 'during': None, 'after': None}
        
        for file in files:
            size = file.stat().st_size
            
            for stage in stages.keys():
                if stage in file.name:
                    stages[stage] = size
                    print(f"   📸 {stage}: {size:,} bytes")
                    break
        
        # تحليل الفروق
        if all(stages.values()):
            before, during, after = stages['before'], stages['during'], stages['after']
            
            if during != before:
                print(f"   ✅ payload يُؤثر على الصفحة")
            if after != during:
                print(f"   🎉 الإصلاحات تعمل!")
            else:
                print(f"   ❌ الإصلاحات لا تعمل")

if __name__ == "__main__":
    test_all_vulnerability_fixes()
