// اختبار النظام الجديد للطلبات المتسلسلة
console.log('🧪 اختبار النظام الجديد للطلبات المتسلسلة...');

async function testSequentialRequests() {
    console.log('🚀 بدء اختبار الطلبات المتسلسلة...');
    
    const vulnerabilities = [
        {
            name: 'XSS Test',
            type: 'XSS',
            payload: '<script>alert("test1")</script>',
            parameter: 'q'
        },
        {
            name: 'SQL Injection Test', 
            type: 'SQL_Injection',
            payload: "' OR 1=1 --",
            parameter: 'id'
        },
        {
            name: 'CSRF Test',
            type: 'CSRF',
            payload: '<form>test</form>',
            parameter: 'token'
        }
    ];
    
    const stages = ['before', 'during', 'after'];
    const reportId = `test_sequential_${Date.now()}`;
    
    console.log(`📊 سيتم اختبار ${vulnerabilities.length} ثغرات × ${stages.length} مراحل = ${vulnerabilities.length * stages.length} طلب`);
    console.log('⏱️ مع انتظار 5 ثواني بين كل طلب، المدة المتوقعة: ~75 ثانية');
    
    let totalRequests = 0;
    let successfulRequests = 0;
    const startTime = Date.now();
    
    // معالجة كل ثغرة على حدة (متسلسل)
    for (let i = 0; i < vulnerabilities.length; i++) {
        const vuln = vulnerabilities[i];
        console.log(`\n🎯 معالجة الثغرة ${i + 1}/${vulnerabilities.length}: ${vuln.name}`);
        
        // معالجة كل مرحلة للثغرة (متسلسل)
        for (let j = 0; j < stages.length; j++) {
            const stage = stages[j];
            totalRequests++;
            
            console.log(`   📸 ${stage} (طلب ${totalRequests}/${vulnerabilities.length * stages.length})`);
            
            try {
                const result = await window.callPythonScreenshotService(
                    'v4_website',
                    'http://testphp.vulnweb.com/search.php',
                    `${stage}_${vuln.type}_sequential_test`,
                    reportId,
                    vuln.name,
                    vuln.type,
                    stage,
                    vuln.payload,
                    vuln.parameter
                );
                
                if (result && result.success) {
                    successfulRequests++;
                    const size = result.file_size || 0;
                    console.log(`      ✅ نجح: ${size} bytes`);
                } else {
                    console.log(`      ❌ فشل: ${result?.error || 'Unknown error'}`);
                }
                
            } catch (error) {
                console.log(`      ❌ خطأ: ${error.message}`);
            }
        }
        
        console.log(`   ✅ انتهت معالجة الثغرة: ${vuln.name}`);
    }
    
    const endTime = Date.now();
    const totalTime = (endTime - startTime) / 1000;
    
    console.log(`\n🎉 انتهى الاختبار!`);
    console.log(`📊 النتائج:`);
    console.log(`   • إجمالي الطلبات: ${totalRequests}`);
    console.log(`   • الطلبات الناجحة: ${successfulRequests}`);
    console.log(`   • معدل النجاح: ${((successfulRequests / totalRequests) * 100).toFixed(1)}%`);
    console.log(`   • الوقت الإجمالي: ${totalTime.toFixed(1)} ثانية`);
    console.log(`   • متوسط الوقت لكل طلب: ${(totalTime / totalRequests).toFixed(1)} ثانية`);
    
    if (successfulRequests === totalRequests) {
        console.log('🎉 جميع الطلبات نجحت! النظام المتسلسل يعمل بشكل مثالي!');
    } else {
        console.log('⚠️ بعض الطلبات فشلت. قد تحتاج لمراجعة النظام.');
    }
}

// تشغيل الاختبار
testSequentialRequests().catch(error => {
    console.error('❌ خطأ في الاختبار:', error);
});
