/**
 * اختبار Python Screenshot Bridge في بيئة Node.js
 */

// استخدام fetch المدمج في Node.js 18+
const fetch = globalThis.fetch;

async function testPythonServerDirect() {
    console.log('🔍 اختبار مباشر لسيرفر Python...');
    
    const serverUrl = 'http://localhost:8000';
    
    try {
        // 1. اختبار health check
        console.log('1️⃣ اختبار health check...');
        const healthResponse = await fetch(`${serverUrl}/health`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json'
            }
        });
        
        if (healthResponse.ok) {
            const healthData = await healthResponse.json();
            console.log('✅ Health check نجح:', healthData);
        } else {
            console.error('❌ Health check فشل:', healthResponse.status, healthResponse.statusText);
            return false;
        }
        
        // 2. اختبار endpoint /v4_website مع بيانات حقيقية
        console.log('2️⃣ اختبار endpoint /v4_website مع بيانات حقيقية...');
        const testData = {
            url: 'http://testphp.vulnweb.com/search.php',
            filename: 'nodejs_test_screenshot',
            report_id: 'nodejs_test_report_123',
            vulnerability_name: 'XSS_Cross_Site_Scripting',
            vulnerability_type: 'XSS',
            stage: 'before',
            payload_data: '<script>alert("NODEJS_TEST")</script>',
            target_parameter: 'searchFor'
        };
        
        console.log('📤 إرسال بيانات الاختبار:', testData);
        
        const testResponse = await fetch(`${serverUrl}/v4_website`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(testData)
        });
        
        console.log('📥 استجابة السيرفر:', {
            status: testResponse.status,
            statusText: testResponse.statusText,
            ok: testResponse.ok
        });
        
        if (testResponse.ok) {
            const responseData = await testResponse.json();
            console.log('✅ اختبار /v4_website نجح:', responseData);
            
            // 3. اختبار الصور الثلاث
            console.log('3️⃣ اختبار الصور الثلاث...');
            const stages = ['before', 'during', 'after'];
            
            for (const stage of stages) {
                console.log(`   📸 اختبار مرحلة ${stage}...`);
                
                const stageData = {
                    ...testData,
                    filename: `nodejs_test_${stage}`,
                    stage: stage,
                    payload_data: stage === 'before' ? null : `<script>alert("${stage.toUpperCase()}_TEST")</script>`
                };
                
                const stageResponse = await fetch(`${serverUrl}/v4_website`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(stageData)
                });
                
                if (stageResponse.ok) {
                    const stageResult = await stageResponse.json();
                    console.log(`   ✅ مرحلة ${stage} نجحت:`, {
                        success: stageResult.success,
                        filename: stageResult.filename,
                        file_size: stageResult.file_size
                    });
                } else {
                    console.log(`   ❌ مرحلة ${stage} فشلت:`, stageResponse.status);
                }
                
                // انتظار بين الطلبات
                await new Promise(resolve => setTimeout(resolve, 2000));
            }
            
            return true;
        } else {
            const errorText = await testResponse.text();
            console.error('❌ اختبار /v4_website فشل:', errorText);
            return false;
        }
        
    } catch (error) {
        console.error('❌ خطأ في الاتصال:', error);
        return false;
    }
}

// تشغيل الاختبار
testPythonServerDirect().then(success => {
    if (success) {
        console.log('🎉 جميع الاختبارات نجحت! السيرفر يعمل بشكل مثالي.');
        console.log('💡 المشكلة قد تكون في النظام الرئيسي وليس في السيرفر.');
    } else {
        console.log('💥 فشل في الاختبارات! هناك مشكلة في السيرفر.');
    }
}).catch(error => {
    console.error('💥 خطأ في تشغيل الاختبار:', error);
});
