<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار التقارير الفعلية v4</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 2px solid #007bff;
            border-radius: 8px;
            background: #f8f9fa;
        }
        .vulnerability {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: white;
        }
        .images-section {
            margin: 20px 0;
            padding: 15px;
            background: #e9ecef;
            border-radius: 5px;
        }
        .image-container {
            margin: 15px 0;
            padding: 10px;
            border: 1px solid #ccc;
            border-radius: 5px;
            background: white;
        }
        .image-container img {
            max-width: 400px;
            max-height: 300px;
            border: 2px solid #007bff;
            border-radius: 5px;
            margin: 10px 0;
        }
        .error-message {
            color: #dc3545;
            font-weight: bold;
            padding: 10px;
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            border-radius: 5px;
            margin: 10px 0;
        }
        .success-message {
            color: #155724;
            font-weight: bold;
            padding: 10px;
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 5px;
            margin: 10px 0;
        }
        .path-info {
            font-family: monospace;
            background: #f8f9fa;
            padding: 5px;
            border-radius: 3px;
            font-size: 12px;
            color: #6c757d;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .stats {
            background: #17a2b8;
            color: white;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔥 اختبار التقارير الفعلية v4</h1>
        <p><strong>الهدف:</strong> اختبار عرض الصور في التقارير الرئيسية والمنفصلة الفعلية</p>
        
        <div class="stats" id="stats">
            <h3>📊 إحصائيات الاختبار:</h3>
            <p>🎯 الثغرات المُختبرة: <span id="vulnCount">0</span></p>
            <p>📸 الصور المُنشأة: <span id="imageCount">0</span></p>
            <p>✅ الصور المعروضة: <span id="displayedCount">0</span></p>
            <p>❌ الصور المفقودة: <span id="missingCount">0</span></p>
        </div>

        <div class="test-section">
            <h2>🧪 اختبار النظام v4 الكامل</h2>
            <button onclick="startFullTest()">🚀 بدء الاختبار الكامل</button>
            <button onclick="testExistingImages()">📸 اختبار الصور الموجودة</button>
            <button onclick="generateActualReport()">📋 إنشاء تقرير فعلي</button>
            
            <div id="testResults"></div>
        </div>

        <div class="test-section">
            <h2>📋 محاكاة التقرير الرئيسي</h2>
            <div id="mainReport"></div>
        </div>

        <div class="test-section">
            <h2>📄 محاكاة التقرير المنفصل</h2>
            <div id="separateReport"></div>
        </div>
    </div>

    <script>
        // بيانات الثغرات الموجودة فعلياً
        const existingVulnerabilities = [
            {
                name: 'XSS Cross Site Scripting',
                type: 'XSS',
                severity: 'High',
                screenshots: {
                    before: './assets/modules/bugbounty/screenshots/real_report_test_1753369845/single_before_XSS_REPORT_TEST_1.png',
                    during: './assets/modules/bugbounty/screenshots/real_report_test_1753369845/single_during_XSS_REPORT_TEST_1.png',
                    after: './assets/modules/bugbounty/screenshots/real_report_test_1753369845/single_after_XSS_REPORT_TEST_1.png'
                },
                visual_proof: {
                    before_screenshot_path: './assets/modules/bugbounty/screenshots/real_report_test_1753369845/single_before_XSS_REPORT_TEST_1.png',
                    during_screenshot_path: './assets/modules/bugbounty/screenshots/real_report_test_1753369845/single_during_XSS_REPORT_TEST_1.png',
                    after_screenshot_path: './assets/modules/bugbounty/screenshots/real_report_test_1753369845/single_after_XSS_REPORT_TEST_1.png'
                }
            },
            {
                name: 'SQL Injection Attack',
                type: 'SQL_Injection',
                severity: 'Critical',
                screenshots: {
                    before: './assets/modules/bugbounty/screenshots/real_report_test_1753369845/single_before_SQL_Injection_REPORT_TEST_2.png',
                    during: './assets/modules/bugbounty/screenshots/real_report_test_1753369845/single_during_SQL_Injection_REPORT_TEST_2.png',
                    after: './assets/modules/bugbounty/screenshots/real_report_test_1753369845/single_after_SQL_Injection_REPORT_TEST_2.png'
                },
                visual_proof: {
                    before_screenshot_path: './assets/modules/bugbounty/screenshots/real_report_test_1753369845/single_before_SQL_Injection_REPORT_TEST_2.png',
                    during_screenshot_path: './assets/modules/bugbounty/screenshots/real_report_test_1753369845/single_during_SQL_Injection_REPORT_TEST_2.png',
                    after_screenshot_path: './assets/modules/bugbounty/screenshots/real_report_test_1753369845/single_after_SQL_Injection_REPORT_TEST_2.png'
                }
            }
        ];

        let stats = {
            vulnCount: 0,
            imageCount: 0,
            displayedCount: 0,
            missingCount: 0
        };

        function updateStats() {
            document.getElementById('vulnCount').textContent = stats.vulnCount;
            document.getElementById('imageCount').textContent = stats.imageCount;
            document.getElementById('displayedCount').textContent = stats.displayedCount;
            document.getElementById('missingCount').textContent = stats.missingCount;
        }

        function startFullTest() {
            document.getElementById('testResults').innerHTML = '<h3>🚀 بدء الاختبار الكامل...</h3>';
            
            // إعادة تعيين الإحصائيات
            stats = { vulnCount: 0, imageCount: 0, displayedCount: 0, missingCount: 0 };
            
            // اختبار الثغرات الموجودة
            testExistingImages();
            
            // إنشاء التقارير
            setTimeout(() => {
                generateMainReport();
                generateSeparateReport();
            }, 1000);
        }

        function testExistingImages() {
            const resultsDiv = document.getElementById('testResults');
            let html = '<h3>📸 اختبار الصور الموجودة:</h3>';
            
            stats.vulnCount = existingVulnerabilities.length;
            
            existingVulnerabilities.forEach((vuln, index) => {
                html += `<div class="vulnerability">`;
                html += `<h4>🎯 الثغرة ${index + 1}: ${vuln.name}</h4>`;
                html += `<p><strong>النوع:</strong> ${vuln.type} | <strong>الخطورة:</strong> ${vuln.severity}</p>`;
                
                html += `<div class="images-section">`;
                html += `<h5>📸 اختبار الصور:</h5>`;
                
                // اختبار screenshots
                if (vuln.screenshots) {
                    html += `<h6>🔍 Screenshots paths:</h6>`;
                    Object.keys(vuln.screenshots).forEach(stage => {
                        const path = vuln.screenshots[stage];
                        stats.imageCount++;
                        
                        html += `<div class="image-container">`;
                        html += `<p><strong>${stage}:</strong></p>`;
                        html += `<div class="path-info">${path}</div>`;
                        html += `<img src="${path}" alt="${stage} screenshot" 
                                     onload="imageLoaded(this)" 
                                     onerror="imageFailed(this, '${path}')">`;
                        html += `</div>`;
                    });
                }
                
                // اختبار visual_proof
                if (vuln.visual_proof) {
                    html += `<h6>🖼️ Visual proof paths:</h6>`;
                    Object.keys(vuln.visual_proof).forEach(key => {
                        if (key.endsWith('_screenshot_path')) {
                            const stage = key.replace('_screenshot_path', '');
                            const path = vuln.visual_proof[key];
                            stats.imageCount++;
                            
                            html += `<div class="image-container">`;
                            html += `<p><strong>${stage} (visual_proof):</strong></p>`;
                            html += `<div class="path-info">${path}</div>`;
                            html += `<img src="${path}" alt="${stage} visual proof" 
                                         onload="imageLoaded(this)" 
                                         onerror="imageFailed(this, '${path}')">`;
                            html += `</div>`;
                        }
                    });
                }
                
                html += `</div>`;
                html += `</div>`;
            });
            
            resultsDiv.innerHTML = html;
            updateStats();
        }

        function imageLoaded(img) {
            stats.displayedCount++;
            updateStats();
            
            const container = img.parentElement;
            const successMsg = document.createElement('div');
            successMsg.className = 'success-message';
            successMsg.textContent = '✅ تم تحميل الصورة بنجاح';
            container.appendChild(successMsg);
        }

        function imageFailed(img, path) {
            stats.missingCount++;
            updateStats();
            
            img.style.display = 'none';
            const container = img.parentElement;
            const errorMsg = document.createElement('div');
            errorMsg.className = 'error-message';
            errorMsg.textContent = `❌ فشل في تحميل الصورة: ${path}`;
            container.appendChild(errorMsg);
        }

        function generateMainReport() {
            const mainReportDiv = document.getElementById('mainReport');
            
            let html = `
                <h3>📋 التقرير الرئيسي</h3>
                <p><strong>تاريخ الإنشاء:</strong> ${new Date().toLocaleString('ar-SA')}</p>
                <p><strong>عدد الثغرات:</strong> ${existingVulnerabilities.length}</p>
            `;
            
            existingVulnerabilities.forEach((vuln, index) => {
                html += `
                    <div class="vulnerability">
                        <h4>🎯 الثغرة ${index + 1}: ${vuln.name}</h4>
                        <p><strong>النوع:</strong> ${vuln.type}</p>
                        <p><strong>الخطورة:</strong> ${vuln.severity}</p>
                        
                        <div class="section visual-changes">
                            <h5>📸 الصور التوضيحية والتأثيرات البصرية</h5>
                            
                            <div class="image-container">
                                <h6>قبل الاستغلال:</h6>
                                <img src="${vuln.screenshots.before}" alt="قبل الاستغلال" 
                                     onload="imageLoaded(this)" onerror="imageFailed(this, '${vuln.screenshots.before}')">
                            </div>
                            
                            <div class="image-container">
                                <h6>أثناء الاستغلال:</h6>
                                <img src="${vuln.screenshots.during}" alt="أثناء الاستغلال"
                                     onload="imageLoaded(this)" onerror="imageFailed(this, '${vuln.screenshots.during}')">
                            </div>
                            
                            <div class="image-container">
                                <h6>بعد الاستغلال:</h6>
                                <img src="${vuln.screenshots.after}" alt="بعد الاستغلال"
                                     onload="imageLoaded(this)" onerror="imageFailed(this, '${vuln.screenshots.after}')">
                            </div>
                        </div>
                    </div>
                `;
            });
            
            mainReportDiv.innerHTML = html;
        }

        function generateSeparateReport() {
            const separateReportDiv = document.getElementById('separateReport');
            
            let html = `
                <h3>📄 التقرير المنفصل</h3>
                <p><strong>تقرير مفصل للثغرات مع الصور</strong></p>
            `;
            
            existingVulnerabilities.forEach((vuln, index) => {
                html += `
                    <div class="vulnerability">
                        <h4>تفاصيل الثغرة ${index + 1}: ${vuln.name}</h4>
                        
                        <div class="section visual-changes">
                            <h5>🖼️ الأدلة البصرية (Visual Proof)</h5>
                            
                            <div class="image-container">
                                <h6>الحالة الأصلية:</h6>
                                <img src="${vuln.visual_proof.before_screenshot_path}" alt="الحالة الأصلية"
                                     onload="imageLoaded(this)" onerror="imageFailed(this, '${vuln.visual_proof.before_screenshot_path}')">
                            </div>
                            
                            <div class="image-container">
                                <h6>عملية الاستغلال:</h6>
                                <img src="${vuln.visual_proof.during_screenshot_path}" alt="عملية الاستغلال"
                                     onload="imageLoaded(this)" onerror="imageFailed(this, '${vuln.visual_proof.during_screenshot_path}')">
                            </div>
                            
                            <div class="image-container">
                                <h6>نتيجة الاستغلال:</h6>
                                <img src="${vuln.visual_proof.after_screenshot_path}" alt="نتيجة الاستغلال"
                                     onload="imageLoaded(this)" onerror="imageFailed(this, '${vuln.visual_proof.after_screenshot_path}')">
                            </div>
                        </div>
                    </div>
                `;
            });
            
            separateReportDiv.innerHTML = html;
        }

        function generateActualReport() {
            alert('🚀 سيتم إنشاء تقرير فعلي باستخدام النظام v4...\n\nهذا يتطلب تشغيل النظام الكامل.');
        }

        // تشغيل الاختبار عند تحميل الصفحة
        window.onload = function() {
            testExistingImages();
        };
    </script>
</body>
</html>
