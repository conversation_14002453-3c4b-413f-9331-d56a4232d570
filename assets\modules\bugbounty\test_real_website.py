#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار سيرفر Python مع مواقع حقيقية لفحص أسماء المجلدات
"""

import requests
import json
import time

def test_admin_page():
    """اختبار صفحة الإدارة"""
    try:
        print("🔍 اختبار صفحة الإدارة...")
        
        test_data = {
            "url": "http://testphp.vulnweb.com/admin",
            "filename": "before_Admin_Access",
            "report_id": f"admin_test_{int(time.time())}",
            "vulnerability_name": "Admin Access",
            "vulnerability_type": "Privilege Escalation",
            "stage": "before",
            "payload_data": None,
            "target_parameter": None
        }
        
        response = requests.post(
            'http://localhost:8000/v4_website',
            json=test_data,
            timeout=30
        )
        
        print(f"📊 Status Code: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"✅ نجح: {result.get('filename')}")
            print(f"📁 مسار الملف: {result.get('file_path')}")
        else:
            print(f"❌ فشل: {response.text}")
        
        return response.status_code == 200
        
    except Exception as e:
        print(f"❌ خطأ في اختبار صفحة الإدارة: {e}")
        return False

def test_main_page():
    """اختبار الصفحة الرئيسية"""
    try:
        print("\n🔍 اختبار الصفحة الرئيسية...")
        
        test_data = {
            "url": "http://testphp.vulnweb.com/",
            "filename": "before_Main_Page_XSS",
            "report_id": f"main_test_{int(time.time())}",
            "vulnerability_name": "XSS Attack",
            "vulnerability_type": "Cross-Site Scripting",
            "stage": "before",
            "payload_data": "<script>alert('xss')</script>",
            "target_parameter": "search"
        }
        
        response = requests.post(
            'http://localhost:8000/v4_website',
            json=test_data,
            timeout=30
        )
        
        print(f"📊 Status Code: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"✅ نجح: {result.get('filename')}")
            print(f"📁 مسار الملف: {result.get('file_path')}")
        else:
            print(f"❌ فشل: {response.text}")
        
        return response.status_code == 200
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الصفحة الرئيسية: {e}")
        return False

def test_login_page():
    """اختبار صفحة تسجيل الدخول"""
    try:
        print("\n🔍 اختبار صفحة تسجيل الدخول...")
        
        test_data = {
            "url": "http://testphp.vulnweb.com/login.php",
            "filename": "during_SQL_Injection",
            "report_id": f"login_test_{int(time.time())}",
            "vulnerability_name": "SQL Injection",
            "vulnerability_type": "Database Injection",
            "stage": "during",
            "payload_data": "' OR '1'='1",
            "target_parameter": "username"
        }
        
        response = requests.post(
            'http://localhost:8000/v4_website',
            json=test_data,
            timeout=30
        )
        
        print(f"📊 Status Code: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"✅ نجح: {result.get('filename')}")
            print(f"📁 مسار الملف: {result.get('file_path')}")
        else:
            print(f"❌ فشل: {response.text}")
        
        return response.status_code == 200
        
    except Exception as e:
        print(f"❌ خطأ في اختبار صفحة تسجيل الدخول: {e}")
        return False

def main():
    """تشغيل جميع الاختبارات"""
    print("🚀 بدء اختبار أسماء المجلدات مع مواقع حقيقية")
    print("=" * 60)
    
    tests = [
        ("صفحة الإدارة", test_admin_page),
        ("الصفحة الرئيسية", test_main_page),
        ("صفحة تسجيل الدخول", test_login_page)
    ]
    
    results = []
    for test_name, test_func in tests:
        result = test_func()
        results.append((test_name, result))
    
    print("\n" + "=" * 60)
    print("📊 نتائج الاختبارات:")
    for test_name, result in results:
        status = "✅ نجح" if result else "❌ فشل"
        print(f"{status} {test_name}")
    
    success_count = sum(1 for _, result in results if result)
    print(f"\n🎯 النتيجة النهائية: {success_count}/{len(results)} اختبارات نجحت")

if __name__ == "__main__":
    main()
