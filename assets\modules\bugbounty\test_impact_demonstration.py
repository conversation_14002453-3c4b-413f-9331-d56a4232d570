#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار إظهار التأثيرات الحقيقية للثغرات بعد الاستغلال
"""

import requests
import json
import time
import os
from pathlib import Path

def test_impact_demonstration():
    """اختبار إظهار التأثيرات الحقيقية للثغرات"""
    
    print("🔥 اختبار إظهار التأثيرات الحقيقية للثغرات بعد الاستغلال")
    print("=" * 70)
    
    # التحقق من سيرفر Python
    try:
        response = requests.get('http://localhost:8000/health', timeout=5)
        if response.status_code == 200:
            print("✅ سيرفر Python يعمل بنجاح")
        else:
            print(f"❌ سيرفر Python لا يعمل: {response.status_code}")
            return
    except Exception as e:
        print(f"❌ خطأ في الاتصال بسيرفر Python: {e}")
        return
    
    # اختبار ثغرة XSS مع إظهار التأثيرات
    print("\n🧪 اختبار XSS مع إظهار التأثيرات الحقيقية...")
    test_xss_impact_demonstration()
    
    # اختبار ثغرة SQL مع إظهار التأثيرات
    print("\n🧪 اختبار SQL Injection مع إظهار التأثيرات الحقيقية...")
    test_sql_impact_demonstration()
    
    print("\n🎉 تم الانتهاء من اختبار التأثيرات!")
    print("📊 قارن أحجام الصور لترى الفرق بين المراحل الثلاث")

def test_xss_impact_demonstration():
    """اختبار XSS مع إظهار التأثيرات"""
    
    report_id = f"impact_test_XSS_{int(time.time())}"
    base_url = "http://testphp.vulnweb.com/search.php"
    payload = '<script>alert("XSS_IMPACT_DEMONSTRATION")</script>'
    parameter = 'searchFor'
    
    # URLs للمراحل الثلاث
    before_url = base_url
    during_url = f"{base_url}?{parameter}={requests.utils.quote(payload)}"
    after_url = f"{during_url}&xss_demo=true&show_alert=true&timestamp={int(time.time())}&exploitation_confirmed=true&impact_demonstrated=true"
    
    print(f"🔗 قبل الاستغلال: {before_url}")
    print(f"🔗 أثناء الاستغلال: {during_url}")
    print(f"🔗 بعد الاستغلال: {after_url}")
    
    # التقاط الصور الثلاث
    stages = [
        ('before', before_url, None),
        ('during', during_url, payload),
        ('after', after_url, payload)
    ]
    
    sizes = {}
    for stage, url, payload_data in stages:
        print(f"📸 التقاط صورة {stage}...")
        
        result = capture_screenshot(
            url=url,
            filename=f"{stage}_XSS_IMPACT",
            report_id=report_id,
            vulnerability_name="XSS Cross Site Scripting",
            vulnerability_type="XSS",
            stage=stage,
            payload_data=payload_data,
            target_parameter=parameter
        )
        
        if result['success']:
            size = result.get('file_size', 0)
            sizes[stage] = size
            print(f"✅ {stage}: نجح - حجم الملف: {size} bytes")
        else:
            print(f"❌ {stage}: فشل - {result.get('error', 'خطأ غير محدد')}")
    
    # تحليل الأحجام
    analyze_size_differences("XSS", sizes)

def test_sql_impact_demonstration():
    """اختبار SQL Injection مع إظهار التأثيرات"""
    
    report_id = f"impact_test_SQL_{int(time.time())}"
    base_url = "http://testphp.vulnweb.com/artists.php"
    payload = "' OR '1'='1' UNION SELECT version(),database(),user() --"
    parameter = 'artist'
    
    # URLs للمراحل الثلاث
    before_url = base_url
    during_url = f"{base_url}?{parameter}={requests.utils.quote(payload)}"
    after_url = f"{during_url}&sql_debug=true&show_errors=true&union_select=true&exploitation_confirmed=true&impact_demonstrated=true"
    
    print(f"🔗 قبل الاستغلال: {before_url}")
    print(f"🔗 أثناء الاستغلال: {during_url}")
    print(f"🔗 بعد الاستغلال: {after_url}")
    
    # التقاط الصور الثلاث
    stages = [
        ('before', before_url, None),
        ('during', during_url, payload),
        ('after', after_url, payload)
    ]
    
    sizes = {}
    for stage, url, payload_data in stages:
        print(f"📸 التقاط صورة {stage}...")
        
        result = capture_screenshot(
            url=url,
            filename=f"{stage}_SQL_IMPACT",
            report_id=report_id,
            vulnerability_name="SQL Injection",
            vulnerability_type="SQL",
            stage=stage,
            payload_data=payload_data,
            target_parameter=parameter
        )
        
        if result['success']:
            size = result.get('file_size', 0)
            sizes[stage] = size
            print(f"✅ {stage}: نجح - حجم الملف: {size} bytes")
        else:
            print(f"❌ {stage}: فشل - {result.get('error', 'خطأ غير محدد')}")
    
    # تحليل الأحجام
    analyze_size_differences("SQL", sizes)

def analyze_size_differences(vuln_type, sizes):
    """تحليل الفروق في أحجام الصور"""
    
    print(f"\n📊 تحليل أحجام صور {vuln_type}:")
    
    if 'before' in sizes and 'during' in sizes and 'after' in sizes:
        before_size = sizes['before']
        during_size = sizes['during']
        after_size = sizes['after']
        
        print(f"   📸 قبل الاستغلال: {before_size:,} bytes")
        print(f"   📸 أثناء الاستغلال: {during_size:,} bytes")
        print(f"   📸 بعد الاستغلال: {after_size:,} bytes")
        
        # تحليل الفروق
        if during_size != before_size:
            print(f"   ✅ الفرق بين قبل وأثناء: {abs(during_size - before_size):,} bytes")
        else:
            print(f"   ⚠️ لا يوجد فرق بين قبل وأثناء الاستغلال")
            
        if after_size != during_size:
            print(f"   🔥 الفرق بين أثناء وبعد: {abs(after_size - during_size):,} bytes")
            print(f"   🎉 ممتاز! صورة بعد الاستغلال تُظهر تأثيرات مختلفة!")
        else:
            print(f"   ❌ لا يوجد فرق بين أثناء وبعد الاستغلال")
            print(f"   ⚠️ قد تحتاج لتحسين إظهار التأثيرات")
    else:
        print(f"   ❌ لم يتم التقاط جميع الصور بنجاح")

def capture_screenshot(url, filename, report_id, vulnerability_name, vulnerability_type, stage, payload_data=None, target_parameter=None):
    """التقاط صورة باستخدام سيرفر Python"""
    
    try:
        data = {
            'url': url,
            'filename': filename,
            'report_id': report_id,
            'vulnerability_name': vulnerability_name,
            'vulnerability_type': vulnerability_type,
            'stage': stage,
            'payload_data': payload_data,
            'target_parameter': target_parameter
        }
        
        response = requests.post(
            'http://localhost:8000/v4_website',
            json=data,
            timeout=30
        )
        
        if response.status_code == 200:
            return response.json()
        else:
            return {
                'success': False,
                'error': f'HTTP {response.status_code}: {response.text[:100]}'
            }
            
    except Exception as e:
        return {
            'success': False,
            'error': str(e)
        }

def check_impact_screenshots():
    """فحص صور التأثيرات"""
    screenshots_dir = Path("screenshots")
    
    if screenshots_dir.exists():
        print(f"\n📁 صور التأثيرات الجديدة:")
        impact_files = list(screenshots_dir.rglob("*IMPACT*.png"))
        
        if impact_files:
            for file in sorted(impact_files):
                size = file.stat().st_size
                print(f"  📸 {file.name} - {size:,} bytes")
        else:
            print("  ❌ لم يتم العثور على صور التأثيرات")
    else:
        print("❌ مجلد الصور غير موجود")

if __name__ == "__main__":
    test_impact_demonstration()
    check_impact_screenshots()
