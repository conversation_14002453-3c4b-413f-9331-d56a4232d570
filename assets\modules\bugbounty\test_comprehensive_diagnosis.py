#!/usr/bin/env python3
"""
🔥 تشخيص شامل للنظام - اختبار عدة ثغرات مع التأثيرات القوية
"""

import json
import time
import requests
from datetime import datetime

def test_vulnerability(vuln_name, vuln_type, payload, stage="all"):
    """اختبار ثغرة واحدة مع جميع المراحل"""
    
    base_url = "http://localhost:8000"
    test_url = "https://httpbin.org/html"
    
    print(f"\n🔥 اختبار {vuln_name} - {vuln_type}")
    print("=" * 60)
    
    if stage == "all":
        stages = ['before', 'during', 'after']
    else:
        stages = [stage]
    
    results = {}
    
    for stage_name in stages:
        print(f"\n📸 [{stage_name.upper()}] اختبار مرحلة {stage_name}")
        
        payload_data = {
            "url": test_url,
            "vulnerability_name": vuln_name,
            "vulnerability_type": vuln_type,
            "stage": stage_name,
            "payload_data": payload,
            "target_parameter": "test_param",
            "filename": f"{stage_name}_{vuln_name.replace(' ', '_')}",
            "report_id": f"diagnosis_{vuln_name.replace(' ', '_')}_{stage_name}_{int(time.time())}"
        }
        
        print(f"📤 إرسال طلب {stage_name}...")
        print(f"📋 Payload: {payload[:100]}...")
        
        try:
            response = requests.post(
                f"{base_url}/v4_website",
                json=payload_data,
                timeout=60
            )
            
            print(f"📥 رد السيرفر: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ نجح {stage_name}: {result.get('message', 'تم بنجاح')}")
                if 'screenshot_path' in result:
                    print(f"📁 مسار الصورة: {result['screenshot_path']}")
                results[stage_name] = {"success": True, "result": result}
            else:
                error_text = response.text
                print(f"❌ فشل {stage_name}: {response.status_code}")
                print(f"📄 خطأ: {error_text[:200]}...")
                results[stage_name] = {"success": False, "error": error_text}
                
        except Exception as e:
            print(f"❌ خطأ في {stage_name}: {e}")
            results[stage_name] = {"success": False, "error": str(e)}
        
        # انتظار بين المراحل
        if stage_name != stages[-1]:
            print("⏳ انتظار 3 ثواني...")
            time.sleep(3)
    
    return results

def main():
    """التشخيص الشامل"""
    print("🔥🔥🔥 بدء التشخيص الشامل للنظام 🔥🔥🔥")
    print(f"🕒 الوقت: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # قائمة الثغرات للاختبار
    vulnerabilities = [
        {
            "name": "XSS_Strong_Effects",
            "type": "Cross-Site Scripting",
            "payload": "<script>alert('🔥 XSS EXPLOITED! 🔥'); document.body.style.background='linear-gradient(45deg, red, orange)'; document.body.innerHTML='<h1 style=\"color:white;text-align:center;font-size:50px;\">🚨 XSS VULNERABILITY EXPLOITED! 🚨</h1>';</script>"
        },
        {
            "name": "SQL_Injection_Test",
            "type": "SQL Injection",
            "payload": "1' UNION SELECT 1,2,3,4,5,6,7,8,9,10,user(),database(),version()--"
        },
        {
            "name": "Command_Injection_Test", 
            "type": "Command Injection",
            "payload": "; whoami; id; uname -a; cat /etc/passwd; ls -la"
        }
    ]
    
    all_results = {}
    
    for vuln in vulnerabilities:
        print(f"\n{'='*80}")
        results = test_vulnerability(
            vuln["name"], 
            vuln["type"], 
            vuln["payload"]
        )
        all_results[vuln["name"]] = results
        
        # تحليل النتائج
        successful_stages = [stage for stage, result in results.items() if result.get("success")]
        failed_stages = [stage for stage, result in results.items() if not result.get("success")]
        
        print(f"\n📊 ملخص {vuln['name']}:")
        print(f"✅ نجح: {successful_stages}")
        print(f"❌ فشل: {failed_stages}")
        
        time.sleep(2)
    
    # ملخص نهائي
    print(f"\n{'='*80}")
    print("🎯 ملخص التشخيص الشامل:")
    print(f"{'='*80}")
    
    for vuln_name, results in all_results.items():
        successful = sum(1 for r in results.values() if r.get("success"))
        total = len(results)
        print(f"📋 {vuln_name}: {successful}/{total} مراحل نجحت")
        
        for stage, result in results.items():
            status = "✅" if result.get("success") else "❌"
            print(f"   {status} {stage}")
    
    print(f"\n🎉 انتهى التشخيص الشامل!")

if __name__ == "__main__":
    main()
