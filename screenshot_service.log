2025-07-29 14:15:35,498 - INFO - 🚀 تم تهيئة خدمة التقاط الصور - الجلسة: 03ba95ae
2025-07-29 14:15:35,498 - INFO - 📁 مجلد الصور: E:\agent ai\agent\assets\modules\bugbounty\screenshots
2025-07-29 14:15:35,498 - INFO - ✅ Selenium متوفر
2025-07-29 14:15:35,509 - INFO - ✅ Playwright متوفر
2025-07-29 14:15:36,390 - INFO - ✅ Pillow متوفر
2025-07-29 14:15:36,390 - INFO - ✅ جميع المتطلبات متوفرة
2025-07-29 14:15:36,390 - INFO - 📸 التقاط صورة واحدة: https://httpbin.org/get
2025-07-29 14:15:44,082 - INFO - ✅ تم تهيئة Playwright browser بنجاح
2025-07-29 14:15:44,083 - INFO - 📸 التقاط صورة Playwright: https://httpbin.org/get - المرحلة: single
2025-07-29 14:15:45,652 - INFO - ✅ تم إنشاء صفحة Playwright بنجاح (المحاولة 1)
2025-07-29 14:15:45,677 - INFO - 🔗 تحميل الصفحة: https://httpbin.org/get
2025-07-29 14:15:57,210 - INFO - 🔥 تطبيق تأثيرات المرحلة: single
2025-07-29 14:15:57,210 - INFO - 🔥 استخدام البيانات الحقيقية: payload=<script>alert('EXPLOITED: None')</script>, type=Unknown
2025-07-29 14:15:57,210 - INFO - 🔥 بدء apply_vulnerability_effects_async - المرحلة: single, الثغرة: None
2025-07-29 14:15:57,212 - WARNING - ⚠️ لا يوجد اسم ثغرة - إنهاء الدالة
2025-07-29 14:15:57,212 - INFO - ⏳ انتظار 10 ثواني لضمان ظهور التأثيرات في مرحلة single...
2025-07-29 14:16:07,232 - INFO - ✅ تم تطبيق التأثيرات البصرية بنجاح
2025-07-29 14:16:07,635 - INFO - ✅ تم حفظ صورة Playwright: E:\agent ai\agent\assets\modules\bugbounty\screenshots\session_03ba95ae\single_test_screenshot.png (32117 bytes)
2025-07-29 14:16:07,635 - INFO - ✅ نجح التقاط الصورة باستخدام Playwright
2025-07-29 14:16:08,772 - INFO - ✅ تم إغلاق Playwright
2025-07-29 14:20:14,392 - INFO - 🚀 تم تهيئة خدمة التقاط الصور - الجلسة: e85321b4
2025-07-29 14:20:14,392 - INFO - 📁 مجلد الصور: E:\agent ai\agent\assets\modules\bugbounty\screenshots
2025-07-29 14:20:14,393 - INFO - ✅ Selenium متوفر
2025-07-29 14:20:14,394 - INFO - ✅ Playwright متوفر
2025-07-29 14:20:14,534 - INFO - ✅ Pillow متوفر
2025-07-29 14:20:14,534 - INFO - ✅ جميع المتطلبات متوفرة
2025-07-29 14:20:14,534 - INFO - 📁 مجلد منفصل للصفحة: E:\agent ai\agent\assets\modules\bugbounty\screenshots\httpbin_org\get_page
2025-07-29 14:20:14,534 - INFO - 🔗 الرابط: https://httpbin.org/get?id=1
2025-07-29 14:20:14,534 - INFO - 📂 الهيكل: httpbin_org/get_page
2025-07-29 14:20:14,534 - INFO - 🎯 بدء التقاط تسلسل صور للثغرة: SQL Injection Test
2025-07-29 14:20:14,534 - INFO - 📷 التقاط صورة before مع تأثيرات قوية: SQL Injection Test
2025-07-29 14:20:15,166 - INFO - ✅ تم تهيئة Playwright browser بنجاح
2025-07-29 14:20:15,166 - INFO - 📸 التقاط صورة Playwright: https://httpbin.org/get?id=1 - المرحلة: before
2025-07-29 14:20:15,982 - INFO - ✅ تم إنشاء صفحة Playwright بنجاح (المحاولة 1)
2025-07-29 14:20:15,989 - INFO - 🔗 تحميل الصفحة: https://httpbin.org/get?id=1
2025-07-29 14:20:21,945 - INFO - 🔥 تطبيق تأثيرات المرحلة: before
2025-07-29 14:20:21,945 - INFO - 🔥 استخدام البيانات الحقيقية: payload=' UNION SELECT 1,2,3,4,5,6,7,8,9,10,11 --, type=SQL Injection
2025-07-29 14:20:21,945 - INFO - 🔥 بدء apply_vulnerability_effects_async - المرحلة: before, الثغرة: SQL Injection Test
2025-07-29 14:20:21,945 - INFO - 🎭 تطبيق تأثيرات حقيقية before للثغرة: SQL Injection Test
2025-07-29 14:20:21,983 - INFO - ⏳ انتظار 10 ثواني لضمان ظهور التأثيرات في مرحلة before...
2025-07-29 14:20:32,001 - INFO - ✅ تم تطبيق التأثيرات البصرية بنجاح
2025-07-29 14:20:32,174 - INFO - ✅ تم حفظ صورة Playwright: E:\agent ai\agent\assets\modules\bugbounty\screenshots\real_server_test\before_SQL_Injection_Test_before.png (35735 bytes)
2025-07-29 14:20:32,174 - INFO - ✅ نجح التقاط صورة BEFORE مع تأثيرات قوية
2025-07-29 14:20:34,184 - INFO - 📷 التقاط صورة during مع تأثيرات قوية: SQL Injection Test
2025-07-29 14:20:34,184 - INFO - 📸 التقاط صورة Playwright: https://httpbin.org/get?id=1 - المرحلة: during
2025-07-29 14:20:34,257 - INFO - ✅ تم إنشاء صفحة Playwright بنجاح (المحاولة 1)
2025-07-29 14:20:34,264 - INFO - 🔗 تحميل الصفحة: https://httpbin.org/get?id=1
2025-07-29 14:20:40,149 - INFO - 🔥 تطبيق تأثيرات المرحلة: during
2025-07-29 14:20:40,149 - INFO - 🔥 استخدام البيانات الحقيقية: payload=' UNION SELECT 1,2,3,4,5,6,7,8,9,10,11 --, type=SQL Injection
2025-07-29 14:20:40,149 - INFO - 🔥 بدء apply_vulnerability_effects_async - المرحلة: during, الثغرة: SQL Injection Test
2025-07-29 14:20:40,149 - INFO - 🎭 تطبيق تأثيرات حقيقية during للثغرة: SQL Injection Test
2025-07-29 14:20:40,155 - INFO - 🤖 النظام الديناميكي يعالج الثغرة: SQL Injection Test
2025-07-29 14:20:40,196 - INFO - ⏳ انتظار 10 ثواني لضمان ظهور التأثيرات في مرحلة during...
2025-07-29 14:20:50,208 - INFO - ✅ تم تطبيق التأثيرات البصرية بنجاح
2025-07-29 14:20:50,458 - INFO - ✅ تم حفظ صورة Playwright: E:\agent ai\agent\assets\modules\bugbounty\screenshots\real_server_test\during_SQL_Injection_Test_during.png (121714 bytes)
2025-07-29 14:20:50,458 - INFO - ✅ نجح التقاط صورة DURING مع تأثيرات قوية
2025-07-29 14:20:52,474 - INFO - 📷 التقاط صورة after مع تأثيرات قوية: SQL Injection Test
2025-07-29 14:20:52,475 - INFO - 📸 التقاط صورة Playwright: https://httpbin.org/get?id=1 - المرحلة: after
2025-07-29 14:20:52,558 - INFO - ✅ تم إنشاء صفحة Playwright بنجاح (المحاولة 1)
2025-07-29 14:20:52,564 - INFO - 🔗 تحميل الصفحة: https://httpbin.org/get?id=1
2025-07-29 14:20:52,564 - INFO - 🔥 مرحلة after - إعادة تحميل قوية مع cache bypass
2025-07-29 14:21:05,157 - INFO - 🔥 تطبيق تأثيرات المرحلة: after
2025-07-29 14:21:05,157 - INFO - 🔥 استخدام البيانات الحقيقية: payload=' UNION SELECT 1,2,3,4,5,6,7,8,9,10,11 --, type=SQL Injection
2025-07-29 14:21:05,161 - INFO - 🔥 بدء apply_vulnerability_effects_async - المرحلة: after, الثغرة: SQL Injection Test
2025-07-29 14:21:05,161 - INFO - 🎭 تطبيق تأثيرات حقيقية after للثغرة: SQL Injection Test
2025-07-29 14:21:05,161 - INFO - 🔥🔥🔥 وصلنا إلى مرحلة AFTER - بدء تطبيق التأثيرات القوية! 🔥🔥🔥
2025-07-29 14:21:05,161 - INFO - 🔥 البيانات الواردة في مرحلة after:
2025-07-29 14:21:05,164 - INFO -    - payload_data: ' UNION SELECT 1,2,3,4,5,6,7,8,9,10,11 --
2025-07-29 14:21:05,164 - INFO -    - vulnerability_type: SQL Injection
2025-07-29 14:21:05,165 - INFO -    - vulnerability_name: SQL Injection Test
2025-07-29 14:21:05,166 - INFO -    - real_payload: ' UNION SELECT 1,2,3,4,5,6,7,8,9,10,11 --
2025-07-29 14:21:05,166 - INFO - 🔥🔥🔥 سيتم تطبيق التأثيرات الآن! 🔥🔥🔥
2025-07-29 14:21:05,168 - INFO - 🔥 تمرير البيانات الحقيقية إلى JavaScript:
2025-07-29 14:21:05,169 - INFO -    - Vulnerability: SQL Injection Test
2025-07-29 14:21:05,170 - INFO -    - Payload: ' UNION SELECT 1,2,3,4,5,6,7,8,9,10,11 --
2025-07-29 14:21:05,171 - INFO -    - Type: SQL Injection
2025-07-29 14:21:05,172 - INFO -    - Evidence count: 4
2025-07-29 14:21:05,206 - ERROR - ❌ خطأ في تطبيق التأثيرات: Page.evaluate: SyntaxError: Unexpected identifier 'MySQL'
    at eval (<anonymous>)
    at UtilityScript.evaluate (<anonymous>:234:30)
    at UtilityScript.<anonymous> (<anonymous>:1:44)
2025-07-29 14:21:05,208 - INFO - ⏳ انتظار 15 ثانية لضمان ظهور جميع التأثيرات في مرحلة after...
2025-07-29 14:21:20,232 - WARNING - ⚠️ تحذير في تنفيذ JavaScript: Page.evaluate: SyntaxError: Illegal return statement
    at eval (<anonymous>)
    at UtilityScript.evaluate (<anonymous>:234:30)
    at UtilityScript.<anonymous> (<anonymous>:1:44)
2025-07-29 14:21:20,414 - INFO - ✅ تم حفظ صورة Playwright: E:\agent ai\agent\assets\modules\bugbounty\screenshots\real_server_test\after_SQL_Injection_Test_after.png (35523 bytes)
2025-07-29 14:21:20,415 - INFO - ✅ نجح التقاط صورة AFTER مع تأثيرات قوية
2025-07-29 14:21:22,428 - ERROR - ❌ خطأ في التقاط تسلسل الصور: [Errno 2] No such file or directory: 'E:\\agent ai\\agent\\assets\\modules\\bugbounty\\screenshots\\httpbin_org\\get_page\\SQL_Injection_Test_metadata.json'
2025-07-29 14:21:22,430 - ERROR - تفاصيل الخطأ: Traceback (most recent call last):
  File "E:\agent ai\agent\assets\modules\bugbounty\screenshot_service.py", line 840, in capture_vulnerability_sequence
    with open(metadata_path, 'w', encoding='utf-8') as f:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'E:\\agent ai\\agent\\assets\\modules\\bugbounty\\screenshots\\httpbin_org\\get_page\\SQL_Injection_Test_metadata.json'

2025-07-29 14:21:23,542 - INFO - ✅ تم إغلاق Playwright
2025-07-29 14:24:20,748 - INFO - 🚀 تم تهيئة خدمة التقاط الصور - الجلسة: 561bc9f5
2025-07-29 14:24:20,748 - INFO - 📁 مجلد الصور: E:\agent ai\agent\assets\modules\bugbounty\screenshots
2025-07-29 14:24:20,749 - INFO - ✅ Selenium متوفر
2025-07-29 14:24:20,751 - INFO - ✅ Playwright متوفر
2025-07-29 14:24:20,919 - INFO - ✅ Pillow متوفر
2025-07-29 14:24:20,919 - INFO - ✅ جميع المتطلبات متوفرة
2025-07-29 14:24:20,919 - INFO - 📁 مجلد منفصل للصفحة: E:\agent ai\agent\assets\modules\bugbounty\screenshots\httpbin_org\get_page
2025-07-29 14:24:20,922 - INFO - 🔗 الرابط: https://httpbin.org/get?id=1
2025-07-29 14:24:20,922 - INFO - 📂 الهيكل: httpbin_org/get_page
2025-07-29 14:24:20,922 - INFO - 🎯 بدء التقاط تسلسل صور للثغرة: SQL Injection Test
2025-07-29 14:24:20,922 - INFO - 📷 التقاط صورة before مع تأثيرات قوية: SQL Injection Test
2025-07-29 14:24:21,680 - INFO - ✅ تم تهيئة Playwright browser بنجاح
2025-07-29 14:24:21,684 - INFO - 📸 التقاط صورة Playwright: https://httpbin.org/get?id=1 - المرحلة: before
2025-07-29 14:24:22,485 - INFO - ✅ تم إنشاء صفحة Playwright بنجاح (المحاولة 1)
2025-07-29 14:24:22,493 - INFO - 🔗 تحميل الصفحة: https://httpbin.org/get?id=1
2025-07-29 14:24:29,072 - INFO - 🔥 تطبيق تأثيرات المرحلة: before
2025-07-29 14:24:29,072 - INFO - 🔥 استخدام البيانات الحقيقية: payload=' UNION SELECT 1,2,3,4,5,6,7,8,9,10,11 --, type=SQL Injection
2025-07-29 14:24:29,072 - INFO - 🔥 بدء apply_vulnerability_effects_async - المرحلة: before, الثغرة: SQL Injection Test
2025-07-29 14:24:29,072 - INFO - 🎭 تطبيق تأثيرات حقيقية before للثغرة: SQL Injection Test
2025-07-29 14:24:29,097 - INFO - ⏳ انتظار 10 ثواني لضمان ظهور التأثيرات في مرحلة before...
2025-07-29 14:24:39,113 - INFO - ✅ تم تطبيق التأثيرات البصرية بنجاح
2025-07-29 14:24:39,308 - INFO - ✅ تم حفظ صورة Playwright: E:\agent ai\agent\assets\modules\bugbounty\screenshots\real_server_test\before_SQL_Injection_Test_before.png (35766 bytes)
2025-07-29 14:24:39,308 - INFO - ✅ نجح التقاط صورة BEFORE مع تأثيرات قوية
2025-07-29 14:24:41,315 - INFO - 📷 التقاط صورة during مع تأثيرات قوية: SQL Injection Test
2025-07-29 14:24:41,315 - INFO - 📸 التقاط صورة Playwright: https://httpbin.org/get?id=1 - المرحلة: during
2025-07-29 14:24:41,400 - INFO - ✅ تم إنشاء صفحة Playwright بنجاح (المحاولة 1)
2025-07-29 14:24:41,407 - INFO - 🔗 تحميل الصفحة: https://httpbin.org/get?id=1
2025-07-29 14:24:47,409 - INFO - 🔥 تطبيق تأثيرات المرحلة: during
2025-07-29 14:24:47,409 - INFO - 🔥 استخدام البيانات الحقيقية: payload=' UNION SELECT 1,2,3,4,5,6,7,8,9,10,11 --, type=SQL Injection
2025-07-29 14:24:47,409 - INFO - 🔥 بدء apply_vulnerability_effects_async - المرحلة: during, الثغرة: SQL Injection Test
2025-07-29 14:24:47,409 - INFO - 🎭 تطبيق تأثيرات حقيقية during للثغرة: SQL Injection Test
2025-07-29 14:24:47,414 - INFO - 🤖 النظام الديناميكي يعالج الثغرة: SQL Injection Test
2025-07-29 14:24:47,439 - INFO - ⏳ انتظار 10 ثواني لضمان ظهور التأثيرات في مرحلة during...
2025-07-29 14:24:57,443 - INFO - ✅ تم تطبيق التأثيرات البصرية بنجاح
2025-07-29 14:24:57,699 - INFO - ✅ تم حفظ صورة Playwright: E:\agent ai\agent\assets\modules\bugbounty\screenshots\real_server_test\during_SQL_Injection_Test_during.png (121753 bytes)
2025-07-29 14:24:57,699 - INFO - ✅ نجح التقاط صورة DURING مع تأثيرات قوية
2025-07-29 14:24:59,711 - INFO - 📷 التقاط صورة after مع تأثيرات قوية: SQL Injection Test
2025-07-29 14:24:59,711 - INFO - 📸 التقاط صورة Playwright: https://httpbin.org/get?id=1 - المرحلة: after
2025-07-29 14:24:59,801 - INFO - ✅ تم إنشاء صفحة Playwright بنجاح (المحاولة 1)
2025-07-29 14:24:59,807 - INFO - 🔗 تحميل الصفحة: https://httpbin.org/get?id=1
2025-07-29 14:24:59,807 - INFO - 🔥 مرحلة after - إعادة تحميل قوية مع cache bypass
2025-07-29 14:25:18,285 - INFO - 🔥 تطبيق تأثيرات المرحلة: after
2025-07-29 14:25:18,287 - INFO - 🔥 استخدام البيانات الحقيقية: payload=' UNION SELECT 1,2,3,4,5,6,7,8,9,10,11 --, type=SQL Injection
2025-07-29 14:25:18,289 - INFO - 🔥 بدء apply_vulnerability_effects_async - المرحلة: after, الثغرة: SQL Injection Test
2025-07-29 14:25:18,289 - INFO - 🎭 تطبيق تأثيرات حقيقية after للثغرة: SQL Injection Test
2025-07-29 14:25:18,291 - INFO - 🔥🔥🔥 وصلنا إلى مرحلة AFTER - بدء تطبيق التأثيرات القوية! 🔥🔥🔥
2025-07-29 14:25:18,291 - INFO - 🔥 البيانات الواردة في مرحلة after:
2025-07-29 14:25:18,291 - INFO -    - payload_data: ' UNION SELECT 1,2,3,4,5,6,7,8,9,10,11 --
2025-07-29 14:25:18,291 - INFO -    - vulnerability_type: SQL Injection
2025-07-29 14:25:18,291 - INFO -    - vulnerability_name: SQL Injection Test
2025-07-29 14:25:18,295 - INFO -    - real_payload: ' UNION SELECT 1,2,3,4,5,6,7,8,9,10,11 --
2025-07-29 14:25:18,295 - INFO - 🔥🔥🔥 سيتم تطبيق التأثيرات الآن! 🔥🔥🔥
2025-07-29 14:25:18,295 - INFO - 🔥 تمرير البيانات الحقيقية إلى JavaScript:
2025-07-29 14:25:18,297 - INFO -    - Vulnerability: SQL Injection Test
2025-07-29 14:25:18,299 - INFO -    - Payload: ' UNION SELECT 1,2,3,4,5,6,7,8,9,10,11 --
2025-07-29 14:25:18,299 - INFO -    - Type: SQL Injection
2025-07-29 14:25:18,300 - INFO -    - Evidence count: 4
2025-07-29 14:25:18,322 - ERROR - ❌ خطأ في تطبيق التأثيرات: Page.evaluate: SyntaxError: Unexpected identifier 'MySQL'
    at eval (<anonymous>)
    at UtilityScript.evaluate (<anonymous>:234:30)
    at UtilityScript.<anonymous> (<anonymous>:1:44)
2025-07-29 14:25:18,323 - INFO - ⏳ انتظار 15 ثانية لضمان ظهور جميع التأثيرات في مرحلة after...
2025-07-29 14:25:33,331 - WARNING - ⚠️ تحذير في تنفيذ JavaScript: Page.evaluate: SyntaxError: Illegal return statement
    at eval (<anonymous>)
    at UtilityScript.evaluate (<anonymous>:234:30)
    at UtilityScript.<anonymous> (<anonymous>:1:44)
2025-07-29 14:25:33,524 - INFO - ✅ تم حفظ صورة Playwright: E:\agent ai\agent\assets\modules\bugbounty\screenshots\real_server_test\after_SQL_Injection_Test_after.png (35513 bytes)
2025-07-29 14:25:33,524 - INFO - ✅ نجح التقاط صورة AFTER مع تأثيرات قوية
2025-07-29 14:25:35,546 - INFO - ✅ تم التقاط 3 صورة للثغرة: SQL Injection Test
2025-07-29 14:25:35,546 - INFO - 📊 إحصائيات الجلسة: {'total_screenshots': 3, 'successful_captures': 3, 'failed_captures': 0, 'selenium_captures': 0, 'playwright_captures': 3}
2025-07-29 14:25:37,208 - INFO - ✅ تم إغلاق Playwright
2025-07-29 14:26:36,612 - INFO - 🚀 تم تهيئة خدمة التقاط الصور - الجلسة: 2f6085e1
2025-07-29 14:26:36,612 - INFO - 📁 مجلد الصور: E:\agent ai\agent\assets\modules\bugbounty\screenshots
2025-07-29 14:26:36,612 - INFO - ✅ Selenium متوفر
2025-07-29 14:26:36,612 - INFO - ✅ Playwright متوفر
2025-07-29 14:26:36,765 - INFO - ✅ Pillow متوفر
2025-07-29 14:26:36,765 - INFO - ✅ جميع المتطلبات متوفرة
2025-07-29 14:26:36,765 - INFO - 📁 مجلد منفصل للصفحة: E:\agent ai\agent\assets\modules\bugbounty\screenshots\httpbin_org\get_page
2025-07-29 14:26:36,765 - INFO - 🔗 الرابط: https://httpbin.org/get?id=1
2025-07-29 14:26:36,765 - INFO - 📂 الهيكل: httpbin_org/get_page
2025-07-29 14:26:36,765 - INFO - 🎯 بدء التقاط تسلسل صور للثغرة: SQL Injection Test
2025-07-29 14:26:36,765 - INFO - 📷 التقاط صورة before مع تأثيرات قوية: SQL Injection Test
2025-07-29 14:26:37,377 - INFO - ✅ تم تهيئة Playwright browser بنجاح
2025-07-29 14:26:37,377 - INFO - 📸 التقاط صورة Playwright: https://httpbin.org/get?id=1 - المرحلة: before
2025-07-29 14:26:38,175 - INFO - ✅ تم إنشاء صفحة Playwright بنجاح (المحاولة 1)
2025-07-29 14:26:38,182 - INFO - 🔗 تحميل الصفحة: https://httpbin.org/get?id=1
2025-07-29 14:26:45,082 - INFO - 🔥 تطبيق تأثيرات المرحلة: before
2025-07-29 14:26:45,096 - INFO - 🔥 استخدام البيانات الحقيقية: payload=' UNION SELECT 1,2,3,4,5,6,7,8,9,10,11 --, type=SQL Injection
2025-07-29 14:26:45,110 - INFO - 🔥 بدء apply_vulnerability_effects_async - المرحلة: before, الثغرة: SQL Injection Test
2025-07-29 14:26:45,110 - INFO - 🎭 تطبيق تأثيرات حقيقية before للثغرة: SQL Injection Test
2025-07-29 14:26:45,185 - INFO - ⏳ انتظار 10 ثواني لضمان ظهور التأثيرات في مرحلة before...
2025-07-29 14:26:55,208 - INFO - ✅ تم تطبيق التأثيرات البصرية بنجاح
2025-07-29 14:26:55,400 - INFO - ✅ تم حفظ صورة Playwright: E:\agent ai\agent\assets\modules\bugbounty\screenshots\real_server_test\before_SQL_Injection_Test_before.png (35864 bytes)
2025-07-29 14:26:55,402 - INFO - ✅ نجح التقاط صورة BEFORE مع تأثيرات قوية
2025-07-29 14:26:57,415 - INFO - 📷 التقاط صورة during مع تأثيرات قوية: SQL Injection Test
2025-07-29 14:26:57,416 - INFO - 📸 التقاط صورة Playwright: https://httpbin.org/get?id=1 - المرحلة: during
2025-07-29 14:26:57,501 - INFO - ✅ تم إنشاء صفحة Playwright بنجاح (المحاولة 1)
2025-07-29 14:26:57,508 - INFO - 🔗 تحميل الصفحة: https://httpbin.org/get?id=1
2025-07-29 14:27:03,500 - INFO - 🔥 تطبيق تأثيرات المرحلة: during
2025-07-29 14:27:03,500 - INFO - 🔥 استخدام البيانات الحقيقية: payload=' UNION SELECT 1,2,3,4,5,6,7,8,9,10,11 --, type=SQL Injection
2025-07-29 14:27:03,500 - INFO - 🔥 بدء apply_vulnerability_effects_async - المرحلة: during, الثغرة: SQL Injection Test
2025-07-29 14:27:03,500 - INFO - 🎭 تطبيق تأثيرات حقيقية during للثغرة: SQL Injection Test
2025-07-29 14:27:03,500 - INFO - 🤖 النظام الديناميكي يعالج الثغرة: SQL Injection Test
2025-07-29 14:27:03,508 - INFO - ⏳ انتظار 10 ثواني لضمان ظهور التأثيرات في مرحلة during...
2025-07-29 14:27:13,517 - INFO - ✅ تم تطبيق التأثيرات البصرية بنجاح
2025-07-29 14:27:13,784 - INFO - ✅ تم حفظ صورة Playwright: E:\agent ai\agent\assets\modules\bugbounty\screenshots\real_server_test\during_SQL_Injection_Test_during.png (121706 bytes)
2025-07-29 14:27:13,786 - INFO - ✅ نجح التقاط صورة DURING مع تأثيرات قوية
2025-07-29 14:27:15,798 - INFO - 📷 التقاط صورة after مع تأثيرات قوية: SQL Injection Test
2025-07-29 14:27:15,798 - INFO - 📸 التقاط صورة Playwright: https://httpbin.org/get?id=1 - المرحلة: after
2025-07-29 14:27:15,856 - INFO - ✅ تم إنشاء صفحة Playwright بنجاح (المحاولة 1)
2025-07-29 14:27:15,863 - INFO - 🔗 تحميل الصفحة: https://httpbin.org/get?id=1
2025-07-29 14:27:15,863 - INFO - 🔥 مرحلة after - إعادة تحميل قوية مع cache bypass
2025-07-29 14:27:30,054 - INFO - 🔥 تطبيق تأثيرات المرحلة: after
2025-07-29 14:27:30,054 - INFO - 🔥 استخدام البيانات الحقيقية: payload=' UNION SELECT 1,2,3,4,5,6,7,8,9,10,11 --, type=SQL Injection
2025-07-29 14:27:30,054 - INFO - 🔥 بدء apply_vulnerability_effects_async - المرحلة: after, الثغرة: SQL Injection Test
2025-07-29 14:27:30,054 - INFO - 🎭 تطبيق تأثيرات حقيقية after للثغرة: SQL Injection Test
2025-07-29 14:27:30,054 - INFO - 🔥🔥🔥 وصلنا إلى مرحلة AFTER - بدء تطبيق التأثيرات القوية! 🔥🔥🔥
2025-07-29 14:27:30,054 - INFO - 🔥 البيانات الواردة في مرحلة after:
2025-07-29 14:27:30,054 - INFO -    - payload_data: ' UNION SELECT 1,2,3,4,5,6,7,8,9,10,11 --
2025-07-29 14:27:30,054 - INFO -    - vulnerability_type: SQL Injection
2025-07-29 14:27:30,054 - INFO -    - vulnerability_name: SQL Injection Test
2025-07-29 14:27:30,054 - INFO -    - real_payload: ' UNION SELECT 1,2,3,4,5,6,7,8,9,10,11 --
2025-07-29 14:27:30,054 - INFO - 🔥🔥🔥 سيتم تطبيق التأثيرات الآن! 🔥🔥🔥
2025-07-29 14:27:30,065 - INFO - 🔥 تمرير البيانات الحقيقية إلى JavaScript:
2025-07-29 14:27:30,066 - INFO -    - Vulnerability: SQL Injection Test
2025-07-29 14:27:30,067 - INFO -    - Payload: ' UNION SELECT 1,2,3,4,5,6,7,8,9,10,11 --
2025-07-29 14:27:30,068 - INFO -    - Type: SQL Injection
2025-07-29 14:27:30,069 - INFO -    - Evidence count: 4
2025-07-29 14:27:30,090 - ERROR - ❌ خطأ في تطبيق التأثيرات: Page.evaluate: SyntaxError: Unexpected identifier 'MySQL'
    at eval (<anonymous>)
    at UtilityScript.evaluate (<anonymous>:234:30)
    at UtilityScript.<anonymous> (<anonymous>:1:44)
2025-07-29 14:27:30,091 - INFO - ⏳ انتظار 15 ثانية لضمان ظهور جميع التأثيرات في مرحلة after...
2025-07-29 14:27:45,095 - WARNING - ⚠️ تحذير في تنفيذ JavaScript: Page.evaluate: SyntaxError: Illegal return statement
    at eval (<anonymous>)
    at UtilityScript.evaluate (<anonymous>:234:30)
    at UtilityScript.<anonymous> (<anonymous>:1:44)
2025-07-29 14:27:45,329 - INFO - ✅ تم حفظ صورة Playwright: E:\agent ai\agent\assets\modules\bugbounty\screenshots\real_server_test\after_SQL_Injection_Test_after.png (35322 bytes)
2025-07-29 14:27:45,329 - INFO - ✅ نجح التقاط صورة AFTER مع تأثيرات قوية
2025-07-29 14:27:47,344 - ERROR - ❌ خطأ في التقاط تسلسل الصور: [Errno 2] No such file or directory: 'E:\\agent ai\\agent\\assets\\modules\\bugbounty\\screenshots\\httpbin_org\\get_page\\SQL_Injection_Test_metadata.json'
2025-07-29 14:27:47,344 - ERROR - تفاصيل الخطأ: Traceback (most recent call last):
  File "E:\agent ai\agent\assets\modules\bugbounty\screenshot_service.py", line 840, in capture_vulnerability_sequence
    with open(metadata_path, 'w', encoding='utf-8') as f:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'E:\\agent ai\\agent\\assets\\modules\\bugbounty\\screenshots\\httpbin_org\\get_page\\SQL_Injection_Test_metadata.json'

2025-07-29 14:27:48,229 - INFO - ✅ تم إغلاق Playwright
2025-07-29 14:28:47,158 - INFO - 🚀 تم تهيئة خدمة التقاط الصور - الجلسة: bc383f98
2025-07-29 14:28:47,158 - INFO - 📁 مجلد الصور: E:\agent ai\agent\assets\modules\bugbounty\screenshots
2025-07-29 14:28:47,159 - INFO - ✅ Selenium متوفر
2025-07-29 14:28:47,159 - INFO - ✅ Playwright متوفر
2025-07-29 14:28:47,309 - INFO - ✅ Pillow متوفر
2025-07-29 14:28:47,309 - INFO - ✅ جميع المتطلبات متوفرة
2025-07-29 14:28:47,964 - INFO - ✅ تم تهيئة Playwright browser بنجاح
2025-07-29 14:28:47,964 - INFO - 📸 التقاط صورة Playwright: https://httpbin.org/get?test=1 - المرحلة: after
2025-07-29 14:28:48,802 - INFO - ✅ تم إنشاء صفحة Playwright بنجاح (المحاولة 1)
2025-07-29 14:28:48,809 - INFO - 🔗 تحميل الصفحة: https://httpbin.org/get?test=1
2025-07-29 14:28:48,809 - INFO - 🔥 مرحلة after - إعادة تحميل قوية مع cache bypass
2025-07-29 14:29:02,057 - INFO - 🔥 تطبيق تأثيرات المرحلة: after
2025-07-29 14:29:02,057 - INFO - 🔥 استخدام البيانات الحقيقية: payload=' UNION SELECT 1,2,3 --, type=SQL Injection
2025-07-29 14:29:02,057 - INFO - 🔥 بدء apply_vulnerability_effects_async - المرحلة: after, الثغرة: SQL Injection
2025-07-29 14:29:02,057 - INFO - 🎭 تطبيق تأثيرات حقيقية after للثغرة: SQL Injection
2025-07-29 14:29:02,057 - INFO - 🔥🔥🔥 وصلنا إلى مرحلة AFTER - بدء تطبيق التأثيرات القوية! 🔥🔥🔥
2025-07-29 14:29:02,057 - INFO - 🔥 البيانات الواردة في مرحلة after:
2025-07-29 14:29:02,057 - INFO -    - payload_data: ' UNION SELECT 1,2,3 --
2025-07-29 14:29:02,065 - INFO -    - vulnerability_type: SQL Injection
2025-07-29 14:29:02,065 - INFO -    - vulnerability_name: SQL Injection
2025-07-29 14:29:02,065 - INFO -    - real_payload: ' UNION SELECT 1,2,3 --
2025-07-29 14:29:02,068 - INFO - 🔥🔥🔥 سيتم تطبيق التأثيرات الآن! 🔥🔥🔥
2025-07-29 14:29:02,070 - INFO - 🔥 تمرير البيانات الحقيقية إلى JavaScript:
2025-07-29 14:29:02,071 - INFO -    - Vulnerability: SQL Injection
2025-07-29 14:29:02,072 - INFO -    - Payload: ' UNION SELECT 1,2,3 --
2025-07-29 14:29:02,072 - INFO -    - Type: SQL Injection
2025-07-29 14:29:02,073 - INFO -    - Evidence count: 4
2025-07-29 14:29:02,102 - ERROR - ❌ خطأ في تطبيق التأثيرات: Page.evaluate: SyntaxError: Unexpected identifier 'MySQL'
    at eval (<anonymous>)
    at UtilityScript.evaluate (<anonymous>:234:30)
    at UtilityScript.<anonymous> (<anonymous>:1:44)
2025-07-29 14:29:02,104 - INFO - ⏳ انتظار 15 ثانية لضمان ظهور جميع التأثيرات في مرحلة after...
2025-07-29 14:29:17,136 - WARNING - ⚠️ تحذير في تنفيذ JavaScript: Page.evaluate: SyntaxError: Illegal return statement
    at eval (<anonymous>)
    at UtilityScript.evaluate (<anonymous>:234:30)
    at UtilityScript.<anonymous> (<anonymous>:1:44)
2025-07-29 14:29:17,336 - INFO - ✅ تم حفظ صورة Playwright: E:\agent ai\agent\assets\modules\bugbounty\screenshots\simple_test\after_simple_sql_test.png (35509 bytes)
2025-07-29 14:29:18,863 - INFO - ✅ تم إغلاق Playwright
2025-07-29 14:29:58,382 - INFO - 🚀 تم تهيئة خدمة التقاط الصور - الجلسة: fda28b01
2025-07-29 14:29:58,382 - INFO - 📁 مجلد الصور: E:\agent ai\agent\assets\modules\bugbounty\screenshots
2025-07-29 14:29:58,384 - INFO - ✅ Selenium متوفر
2025-07-29 14:29:58,385 - INFO - ✅ Playwright متوفر
2025-07-29 14:29:58,555 - INFO - ✅ Pillow متوفر
2025-07-29 14:29:58,555 - INFO - ✅ جميع المتطلبات متوفرة
2025-07-29 14:29:59,238 - INFO - ✅ تم تهيئة Playwright browser بنجاح
2025-07-29 14:29:59,238 - INFO - 📸 التقاط صورة Playwright: https://httpbin.org/get?test=1 - المرحلة: after
2025-07-29 14:30:00,049 - INFO - ✅ تم إنشاء صفحة Playwright بنجاح (المحاولة 1)
2025-07-29 14:30:00,056 - INFO - 🔗 تحميل الصفحة: https://httpbin.org/get?test=1
2025-07-29 14:30:00,057 - INFO - 🔥 مرحلة after - إعادة تحميل قوية مع cache bypass
2025-07-29 14:30:13,037 - INFO - 🔥 تطبيق تأثيرات المرحلة: after
2025-07-29 14:30:13,037 - INFO - 🔥 استخدام البيانات الحقيقية: payload=' UNION SELECT 1,2,3 --, type=SQL Injection
2025-07-29 14:30:13,037 - INFO - 🔥 بدء apply_vulnerability_effects_async - المرحلة: after, الثغرة: SQL Injection
2025-07-29 14:30:13,037 - INFO - 🎭 تطبيق تأثيرات حقيقية after للثغرة: SQL Injection
2025-07-29 14:30:13,037 - INFO - 🔥🔥🔥 وصلنا إلى مرحلة AFTER - بدء تطبيق التأثيرات القوية! 🔥🔥🔥
2025-07-29 14:30:13,037 - INFO - 🔥 البيانات الواردة في مرحلة after:
2025-07-29 14:30:13,037 - INFO -    - payload_data: ' UNION SELECT 1,2,3 --
2025-07-29 14:30:13,037 - INFO -    - vulnerability_type: SQL Injection
2025-07-29 14:30:13,037 - INFO -    - vulnerability_name: SQL Injection
2025-07-29 14:30:13,037 - INFO -    - real_payload: ' UNION SELECT 1,2,3 --
2025-07-29 14:30:13,037 - INFO - 🔥🔥🔥 سيتم تطبيق التأثيرات الآن! 🔥🔥🔥
2025-07-29 14:30:13,037 - INFO - 🔥 تمرير البيانات الحقيقية إلى JavaScript:
2025-07-29 14:30:13,037 - INFO -    - Vulnerability: SQL Injection
2025-07-29 14:30:13,037 - INFO -    - Payload: ' UNION SELECT 1,2,3 --
2025-07-29 14:30:13,037 - INFO -    - Type: SQL Injection
2025-07-29 14:30:13,037 - INFO -    - Evidence count: 4
2025-07-29 14:30:13,622 - INFO - ⏳ انتظار 15 ثانية لضمان ظهور جميع التأثيرات والدلائل في الصورة...
2025-07-29 14:30:28,685 - INFO - ✅ فحص العناصر المرئية: {'exploitContainers': 1, 'proofBanners': 0, 'bigTexts': 0, 'bodyBorder': '10px solid red', 'documentTitle': '🚨 HACKED - SQL Injection EXPLOITED 🚨', 'realServerResponseFound': True, 'realServerResponseContent': '🚨 HACKED - SQL Injection EXPLOITED 🚨{\n  "args": {\n    "cache_bypass": "1753788600", \n    "test": "'}
2025-07-29 14:30:28,685 - INFO - ⏳ انتظار 15 ثانية لضمان ظهور جميع التأثيرات في مرحلة after...
2025-07-29 14:30:43,697 - WARNING - ⚠️ تحذير في تنفيذ JavaScript: Page.evaluate: SyntaxError: Illegal return statement
    at eval (<anonymous>)
    at UtilityScript.evaluate (<anonymous>:234:30)
    at UtilityScript.<anonymous> (<anonymous>:1:44)
2025-07-29 14:30:44,403 - INFO - ✅ تم حفظ صورة Playwright: E:\agent ai\agent\assets\modules\bugbounty\screenshots\simple_test\after_simple_sql_test.png (396444 bytes)
2025-07-29 14:30:46,005 - INFO - ✅ تم إغلاق Playwright
2025-07-29 14:32:16,794 - INFO - 🚀 تم تهيئة خدمة التقاط الصور - الجلسة: 0eb63fbf
2025-07-29 14:32:16,794 - INFO - 📁 مجلد الصور: E:\agent ai\agent\assets\modules\bugbounty\screenshots
2025-07-29 14:32:16,797 - INFO - ✅ Selenium متوفر
2025-07-29 14:32:16,800 - INFO - ✅ Playwright متوفر
2025-07-29 14:32:17,007 - INFO - ✅ Pillow متوفر
2025-07-29 14:32:17,009 - INFO - ✅ جميع المتطلبات متوفرة
2025-07-29 14:32:17,805 - INFO - ✅ تم تهيئة Playwright browser بنجاح
2025-07-29 14:32:17,805 - INFO - 📸 التقاط صورة Playwright: https://httpbin.org/get?test=1 - المرحلة: after
2025-07-29 14:32:18,619 - INFO - ✅ تم إنشاء صفحة Playwright بنجاح (المحاولة 1)
2025-07-29 14:32:18,626 - INFO - 🔗 تحميل الصفحة: https://httpbin.org/get?test=1
2025-07-29 14:32:18,626 - INFO - 🔥 مرحلة after - إعادة تحميل قوية مع cache bypass
2025-07-29 14:32:31,516 - INFO - 🔥 تطبيق تأثيرات المرحلة: after
2025-07-29 14:32:31,517 - INFO - 🔥 استخدام البيانات الحقيقية: payload=' UNION SELECT 1,2,3 --, type=SQL Injection
2025-07-29 14:32:31,518 - INFO - 🔥 بدء apply_vulnerability_effects_async - المرحلة: after, الثغرة: SQL Injection
2025-07-29 14:32:31,519 - INFO - 🎭 تطبيق تأثيرات حقيقية after للثغرة: SQL Injection
2025-07-29 14:32:31,519 - INFO - 🔥🔥🔥 وصلنا إلى مرحلة AFTER - بدء تطبيق التأثيرات القوية! 🔥🔥🔥
2025-07-29 14:32:31,520 - INFO - 🔥 البيانات الواردة في مرحلة after:
2025-07-29 14:32:31,522 - INFO -    - payload_data: ' UNION SELECT 1,2,3 --
2025-07-29 14:32:31,522 - INFO -    - vulnerability_type: SQL Injection
2025-07-29 14:32:31,523 - INFO -    - vulnerability_name: SQL Injection
2025-07-29 14:32:31,524 - INFO -    - real_payload: ' UNION SELECT 1,2,3 --
2025-07-29 14:32:31,524 - INFO - 🔥🔥🔥 سيتم تطبيق التأثيرات الآن! 🔥🔥🔥
2025-07-29 14:32:31,526 - INFO - 🔥 تمرير البيانات الحقيقية إلى JavaScript:
2025-07-29 14:32:31,526 - INFO -    - Vulnerability: SQL Injection
2025-07-29 14:32:31,528 - INFO -    - Payload: ' UNION SELECT 1,2,3 --
2025-07-29 14:32:31,528 - INFO -    - Type: SQL Injection
2025-07-29 14:32:31,529 - INFO -    - Evidence count: 4
2025-07-29 14:32:31,615 - INFO - ⏳ انتظار 15 ثانية لضمان ظهور جميع التأثيرات والدلائل في الصورة...
2025-07-29 14:32:46,655 - INFO - ✅ فحص العناصر المرئية: {'exploitContainers': 1, 'proofBanners': 0, 'bigTexts': 0, 'bodyBorder': '10px solid red', 'documentTitle': '🚨 HACKED - SQL Injection EXPLOITED 🚨', 'realServerResponseFound': True, 'realServerResponseContent': '🚨 HACKED - SQL Injection EXPLOITED 🚨{\n  "args": {\n    "cache_bypass": "1753788738", \n    "test": "'}
2025-07-29 14:32:46,657 - INFO - ⏳ انتظار 15 ثانية لضمان ظهور جميع التأثيرات في مرحلة after...
2025-07-29 14:33:01,682 - WARNING - ⚠️ تحذير في تنفيذ JavaScript: Page.evaluate: SyntaxError: Illegal return statement
    at eval (<anonymous>)
    at UtilityScript.evaluate (<anonymous>:234:30)
    at UtilityScript.<anonymous> (<anonymous>:1:44)
2025-07-29 14:33:02,044 - INFO - ✅ تم حفظ صورة Playwright: E:\agent ai\agent\assets\modules\bugbounty\screenshots\simple_test\after_simple_sql_test.png (398315 bytes)
2025-07-29 14:33:03,262 - INFO - ✅ تم إغلاق Playwright
2025-07-29 14:33:39,149 - INFO - 🚀 تم تهيئة خدمة التقاط الصور - الجلسة: c1b4be7e
2025-07-29 14:33:39,149 - INFO - 📁 مجلد الصور: E:\agent ai\agent\assets\modules\bugbounty\screenshots
2025-07-29 14:33:39,150 - INFO - ✅ Selenium متوفر
2025-07-29 14:33:39,150 - INFO - ✅ Playwright متوفر
2025-07-29 14:33:39,310 - INFO - ✅ Pillow متوفر
2025-07-29 14:33:39,310 - INFO - ✅ جميع المتطلبات متوفرة
2025-07-29 14:33:39,928 - INFO - ✅ تم تهيئة Playwright browser بنجاح
2025-07-29 14:33:39,928 - INFO - 📸 التقاط صورة Playwright: https://httpbin.org/get?id=1 - المرحلة: after
2025-07-29 14:33:40,742 - INFO - ✅ تم إنشاء صفحة Playwright بنجاح (المحاولة 1)
2025-07-29 14:33:40,749 - INFO - 🔗 تحميل الصفحة: https://httpbin.org/get?id=1
2025-07-29 14:33:40,749 - INFO - 🔥 مرحلة after - إعادة تحميل قوية مع cache bypass
2025-07-29 14:33:53,323 - INFO - 🔥 تطبيق تأثيرات المرحلة: after
2025-07-29 14:33:53,323 - INFO - 🔥 استخدام البيانات الحقيقية: payload=' UNION SELECT 1,2,3,4,5 --, type=SQL Injection
2025-07-29 14:33:53,323 - INFO - 🔥 بدء apply_vulnerability_effects_async - المرحلة: after, الثغرة: SQL Injection
2025-07-29 14:33:53,323 - INFO - 🎭 تطبيق تأثيرات حقيقية after للثغرة: SQL Injection
2025-07-29 14:33:53,323 - INFO - 🔥🔥🔥 وصلنا إلى مرحلة AFTER - بدء تطبيق التأثيرات القوية! 🔥🔥🔥
2025-07-29 14:33:53,328 - INFO - 🔥 البيانات الواردة في مرحلة after:
2025-07-29 14:33:53,328 - INFO -    - payload_data: ' UNION SELECT 1,2,3,4,5 --
2025-07-29 14:33:53,328 - INFO -    - vulnerability_type: SQL Injection
2025-07-29 14:33:53,328 - INFO -    - vulnerability_name: SQL Injection
2025-07-29 14:33:53,328 - INFO -    - real_payload: ' UNION SELECT 1,2,3,4,5 --
2025-07-29 14:33:53,328 - INFO - 🔥🔥🔥 سيتم تطبيق التأثيرات الآن! 🔥🔥🔥
2025-07-29 14:33:53,334 - INFO - 🔥 تمرير البيانات الحقيقية إلى JavaScript:
2025-07-29 14:33:53,335 - INFO -    - Vulnerability: SQL Injection
2025-07-29 14:33:53,335 - INFO -    - Payload: ' UNION SELECT 1,2,3,4,5 --
2025-07-29 14:33:53,336 - INFO -    - Type: SQL Injection
2025-07-29 14:33:53,337 - INFO -    - Evidence count: 4
2025-07-29 14:33:53,418 - INFO - ⏳ انتظار 15 ثانية لضمان ظهور جميع التأثيرات والدلائل في الصورة...
2025-07-29 14:34:08,440 - INFO - ✅ فحص العناصر المرئية: {'exploitContainers': 1, 'proofBanners': 0, 'bigTexts': 0, 'bodyBorder': '10px solid red', 'documentTitle': '🚨 HACKED - SQL Injection EXPLOITED 🚨', 'realServerResponseFound': True, 'realServerResponseContent': '🚨 HACKED - SQL Injection EXPLOITED 🚨{\n  "args": {\n    "cache_bypass": "1753788820", \n    "id": "1"'}
2025-07-29 14:34:08,442 - INFO - ⏳ انتظار 15 ثانية لضمان ظهور جميع التأثيرات في مرحلة after...
2025-07-29 14:34:23,455 - WARNING - ⚠️ تحذير في تنفيذ JavaScript: Page.evaluate: SyntaxError: Illegal return statement
    at eval (<anonymous>)
    at UtilityScript.evaluate (<anonymous>:234:30)
    at UtilityScript.<anonymous> (<anonymous>:1:44)
2025-07-29 14:34:23,833 - INFO - ✅ تم حفظ صورة Playwright: E:\agent ai\agent\assets\modules\bugbounty\screenshots\final_test_1\after_final_test_sql_injection.png (399381 bytes)
2025-07-29 14:34:25,968 - INFO - 📸 التقاط صورة Playwright: https://httpbin.org/get?search=test - المرحلة: after
2025-07-29 14:34:26,048 - INFO - ✅ تم إنشاء صفحة Playwright بنجاح (المحاولة 1)
2025-07-29 14:34:26,058 - INFO - 🔗 تحميل الصفحة: https://httpbin.org/get?search=test
2025-07-29 14:34:26,058 - INFO - 🔥 مرحلة after - إعادة تحميل قوية مع cache bypass
2025-07-29 14:34:38,751 - INFO - 🔥 تطبيق تأثيرات المرحلة: after
2025-07-29 14:34:38,751 - INFO - 🔥 استخدام البيانات الحقيقية: payload=<script>alert("XSS")</script>, type=Cross-Site Scripting
2025-07-29 14:34:38,751 - INFO - 🔥 بدء apply_vulnerability_effects_async - المرحلة: after, الثغرة: XSS Attack
2025-07-29 14:34:38,754 - INFO - 🎭 تطبيق تأثيرات حقيقية after للثغرة: XSS Attack
2025-07-29 14:34:38,754 - INFO - 🔥🔥🔥 وصلنا إلى مرحلة AFTER - بدء تطبيق التأثيرات القوية! 🔥🔥🔥
2025-07-29 14:34:38,754 - INFO - 🔥 البيانات الواردة في مرحلة after:
2025-07-29 14:34:38,757 - INFO -    - payload_data: <script>alert("XSS")</script>
2025-07-29 14:34:38,757 - INFO -    - vulnerability_type: Cross-Site Scripting
2025-07-29 14:34:38,757 - INFO -    - vulnerability_name: XSS Attack
2025-07-29 14:34:38,757 - INFO -    - real_payload: <script>alert("XSS")</script>
2025-07-29 14:34:38,757 - INFO - 🔥🔥🔥 سيتم تطبيق التأثيرات الآن! 🔥🔥🔥
2025-07-29 14:34:38,761 - INFO - 🔥 تمرير البيانات الحقيقية إلى JavaScript:
2025-07-29 14:34:38,762 - INFO -    - Vulnerability: XSS Attack
2025-07-29 14:34:38,764 - INFO -    - Payload: <script>alert("XSS")</script>
2025-07-29 14:34:38,765 - INFO -    - Type: Cross-Site Scripting
2025-07-29 14:34:38,766 - INFO -    - Evidence count: 4
2025-07-29 14:34:39,074 - INFO - ⏳ انتظار 15 ثانية لضمان ظهور جميع التأثيرات والدلائل في الصورة...
2025-07-29 14:34:54,104 - INFO - ✅ فحص العناصر المرئية: {'exploitContainers': 1, 'proofBanners': 0, 'bigTexts': 0, 'bodyBorder': '10px solid red', 'documentTitle': '🚨 HACKED - Cross-Site Scripting EXPLOITED 🚨', 'realServerResponseFound': True, 'realServerResponseContent': '🚨 HACKED - Cross-Site Scripting EXPLOITED 🚨{\n  "args": {\n    "cache_bypass": "1753788866", \n    "s'}
2025-07-29 14:34:54,105 - INFO - ⏳ انتظار 15 ثانية لضمان ظهور جميع التأثيرات في مرحلة after...
2025-07-29 14:35:09,116 - WARNING - ⚠️ تحذير في تنفيذ JavaScript: Page.evaluate: SyntaxError: Illegal return statement
    at eval (<anonymous>)
    at UtilityScript.evaluate (<anonymous>:234:30)
    at UtilityScript.<anonymous> (<anonymous>:1:44)
2025-07-29 14:35:09,510 - INFO - ✅ تم حفظ صورة Playwright: E:\agent ai\agent\assets\modules\bugbounty\screenshots\final_test_2\after_final_test_xss_attack.png (390653 bytes)
2025-07-29 14:35:11,613 - INFO - 📸 التقاط صورة Playwright: https://httpbin.org/get?cmd=whoami - المرحلة: after
2025-07-29 14:35:11,684 - INFO - ✅ تم إنشاء صفحة Playwright بنجاح (المحاولة 1)
2025-07-29 14:35:11,694 - INFO - 🔗 تحميل الصفحة: https://httpbin.org/get?cmd=whoami
2025-07-29 14:35:11,695 - INFO - 🔥 مرحلة after - إعادة تحميل قوية مع cache bypass
2025-07-29 14:35:24,426 - INFO - 🔥 تطبيق تأثيرات المرحلة: after
2025-07-29 14:35:24,426 - INFO - 🔥 استخدام البيانات الحقيقية: payload=; ls -la, type=Command Injection
2025-07-29 14:35:24,426 - INFO - 🔥 بدء apply_vulnerability_effects_async - المرحلة: after, الثغرة: Command Injection
2025-07-29 14:35:24,426 - INFO - 🎭 تطبيق تأثيرات حقيقية after للثغرة: Command Injection
2025-07-29 14:35:24,426 - INFO - 🔥🔥🔥 وصلنا إلى مرحلة AFTER - بدء تطبيق التأثيرات القوية! 🔥🔥🔥
2025-07-29 14:35:24,426 - INFO - 🔥 البيانات الواردة في مرحلة after:
2025-07-29 14:35:24,426 - INFO -    - payload_data: ; ls -la
2025-07-29 14:35:24,426 - INFO -    - vulnerability_type: Command Injection
2025-07-29 14:35:24,435 - INFO -    - vulnerability_name: Command Injection
2025-07-29 14:35:24,435 - INFO -    - real_payload: ; ls -la
2025-07-29 14:35:24,435 - INFO - 🔥🔥🔥 سيتم تطبيق التأثيرات الآن! 🔥🔥🔥
2025-07-29 14:35:24,438 - INFO - 🔥 تمرير البيانات الحقيقية إلى JavaScript:
2025-07-29 14:35:24,438 - INFO -    - Vulnerability: Command Injection
2025-07-29 14:35:24,439 - INFO -    - Payload: ; ls -la
2025-07-29 14:35:24,440 - INFO -    - Type: Command Injection
2025-07-29 14:35:24,441 - INFO -    - Evidence count: 4
2025-07-29 14:35:24,524 - INFO - ⏳ انتظار 15 ثانية لضمان ظهور جميع التأثيرات والدلائل في الصورة...
2025-07-29 14:35:39,568 - INFO - ✅ فحص العناصر المرئية: {'exploitContainers': 1, 'proofBanners': 0, 'bigTexts': 0, 'bodyBorder': '10px solid red', 'documentTitle': '🚨 HACKED - Command Injection EXPLOITED 🚨', 'realServerResponseFound': True, 'realServerResponseContent': '🚨 HACKED - Command Injection EXPLOITED 🚨{\n  "args": {\n    "cache_bypass": "1753788911", \n    "cmd"'}
2025-07-29 14:35:39,569 - INFO - ⏳ انتظار 15 ثانية لضمان ظهور جميع التأثيرات في مرحلة after...
2025-07-29 14:35:54,597 - WARNING - ⚠️ تحذير في تنفيذ JavaScript: Page.evaluate: SyntaxError: Illegal return statement
    at eval (<anonymous>)
    at UtilityScript.evaluate (<anonymous>:234:30)
    at UtilityScript.<anonymous> (<anonymous>:1:44)
2025-07-29 14:35:54,947 - INFO - ✅ تم حفظ صورة Playwright: E:\agent ai\agent\assets\modules\bugbounty\screenshots\final_test_3\after_final_test_command_injection.png (393300 bytes)
2025-07-29 14:35:57,893 - INFO - ✅ تم إغلاق Playwright
