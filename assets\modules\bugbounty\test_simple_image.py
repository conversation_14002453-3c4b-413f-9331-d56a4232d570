#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json

def test_simple_image():
    """اختبار إنشاء صورة واحدة"""
    
    data = {
        'url': 'http://testphp.vulnweb.com/search.php',
        'filename': 'test_image_display',
        'report_id': 'image_display_test',
        'vulnerability_name': 'XSS Cross Site Scripting',
        'vulnerability_type': 'XSS',
        'stage': 'after',
        'payload_data': '<script>alert("TEST")</script>',
        'target_parameter': 'searchFor'
    }

    print('🧪 اختبار إنشاء صورة للتحقق من الإصلاحات...')

    try:
        response = requests.post('http://localhost:8000/v4_website', json=data, timeout=30)
        result = response.json()
        
        if result.get('success'):
            path = result.get('file_path', '')
            size = result.get('file_size', 0)
            print(f'✅ تم إنشاء الصورة: {size} bytes')
            print(f'📁 المسار: {path}')
            
            # تحويل إلى مسار نسبي
            if path:
                relative_path = path.replace('E:\\المساعد ai\\مساعد 2\\', './')
                print(f'🔗 المسار النسبي: {relative_path}')
                
                # محاكاة بيانات الثغرة
                vuln_data = {
                    'name': 'XSS Cross Site Scripting',
                    'type': 'XSS',
                    'screenshots': {
                        'after': relative_path
                    },
                    'visual_proof': {
                        'after_screenshot_path': relative_path
                    }
                }
                
                print('\n🔍 محاكاة البحث عن الصورة في النظام v4:')
                print(f'   📸 screenshots.after: {vuln_data["screenshots"]["after"]}')
                print(f'   🖼️ visual_proof.after_screenshot_path: {vuln_data["visual_proof"]["after_screenshot_path"]}')
                
        else:
            print(f'❌ فشل: {result.get("error", "Unknown")}')
            
    except Exception as e:
        print(f'❌ خطأ: {e}')

if __name__ == "__main__":
    test_simple_image()
