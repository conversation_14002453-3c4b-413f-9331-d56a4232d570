#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json
import time

def test_real_vulnerability_impacts():
    """اختبار التأثيرات الحقيقية للثغرات المختلفة"""
    
    print("🔥 اختبار التأثيرات الحقيقية للثغرات")
    print("🎯 لنرى الدليل الحقيقي على وجود الثغرات في الصور")
    print("=" * 70)
    
    vulnerabilities = [
        {
            'name': 'XSS Cross Site Scripting',
            'type': 'XSS',
            'payload': '<script>alert("XSS_REAL_PROOF")</script>',
            'param': 'searchFor',
            'description': 'يجب أن تُظهر: تنفيذ JavaScript، alert، معلومات المتصفح'
        },
        {
            'name': 'SQL Injection Attack',
            'type': 'SQL_Injection',
            'payload': "' UNION SELECT version(), database(), user() --",
            'param': 'id',
            'description': 'يجب أن تُظهر: بيانات قاعدة البيانات، جداول، معلومات حساسة'
        },
        {
            'name': 'Path Traversal Attack',
            'type': 'Path_Traversal',
            'payload': '../../../etc/passwd',
            'param': 'file',
            'description': 'يجب أن تُظهر: محتوى ملفات النظام، /etc/passwd، معلومات المستخدمين'
        },
        {
            'name': 'Command Injection Attack',
            'type': 'Command_Injection',
            'payload': '; whoami; id; uname -a',
            'param': 'cmd',
            'description': 'يجب أن تُظهر: نتائج الأوامر، معلومات النظام، صلاحيات المستخدم'
        }
    ]
    
    for i, vuln in enumerate(vulnerabilities, 1):
        print(f"\n{'='*70}")
        print(f"🎯 اختبار {i}/{len(vulnerabilities)}: {vuln['name']}")
        print(f"🔍 النوع: {vuln['type']}")
        print(f"💉 Payload: {vuln['payload']}")
        print(f"📝 المعامل: {vuln['param']}")
        print(f"📊 المتوقع: {vuln['description']}")
        print(f"{'='*70}")
        
        # اختبار المراحل الثلاث
        test_vulnerability_stages(vuln, i)

def test_vulnerability_stages(vuln, vuln_num):
    """اختبار المراحل الثلاث لثغرة واحدة"""
    
    report_id = f"real_impact_{vuln_num}_{int(time.time())}"
    
    stages = ['before', 'during', 'after']
    results = {}
    
    for stage in stages:
        print(f"\n📸 التقاط صورة {stage}...")
        
        data = {
            'url': 'http://testphp.vulnweb.com/search.php',
            'filename': f'{stage}_{vuln["type"]}_REAL',
            'report_id': report_id,
            'vulnerability_name': vuln['name'],
            'vulnerability_type': vuln['type'],
            'stage': stage,
            'payload_data': vuln['payload'],
            'target_parameter': vuln['param']
        }
        
        result = capture_and_analyze(data, stage, vuln['type'])
        
        if result['success']:
            size = result.get('file_size', 0)
            results[stage] = size
            
            print(f"   ✅ نجح: {size:,} bytes")
            
            # تحليل فوري للمحتوى
            if stage == 'before':
                print(f"   📊 الموقع الأصلي")
            elif stage == 'during':
                before_size = results.get('before', 0)
                if size != before_size:
                    print(f"   🔄 تغيير في المحتوى - payload مُطبق")
                    if size > before_size + 10000:
                        print(f"   🎯 محتوى إضافي كبير - قد يحتوي على دليل الثغرة")
                else:
                    print(f"   ⚠️ لا يوجد تغيير - payload قد لا يعمل")
            elif stage == 'after':
                during_size = results.get('during', 0)
                if size > during_size + 5000:
                    print(f"   🎉 تأثيرات إضافية قوية - دليل واضح على الثغرة")
                elif size != during_size:
                    print(f"   📈 تأثيرات إضافية متوسطة")
                else:
                    print(f"   ❌ نفس محتوى 'أثناء' - لا توجد تأثيرات إضافية")
        else:
            print(f"   ❌ فشل: {result.get('error', 'خطأ غير محدد')}")
            results[stage] = 0
    
    # تحليل شامل للثغرة
    analyze_vulnerability_impact(vuln, results)

def capture_and_analyze(data, stage, vuln_type):
    """التقاط صورة مع تحليل مفصل"""
    
    try:
        print(f"      🔍 إرسال طلب للسيرفر...")
        
        response = requests.post(
            'http://localhost:8000/v4_website',
            json=data,
            timeout=60
        )
        
        if response.status_code == 200:
            result = response.json()
            
            if result.get('success'):
                print(f"      ✅ استجابة ناجحة من السيرفر")
                
                # تحليل إضافي حسب نوع الثغرة
                size = result.get('file_size', 0)
                if stage == 'after':
                    if vuln_type == 'XSS' and size > 80000:
                        print(f"      🎯 حجم كبير - قد يحتوي على JavaScript وalerts")
                    elif vuln_type == 'SQL_Injection' and size > 70000:
                        print(f"      🎯 حجم كبير - قد يحتوي على بيانات قاعدة البيانات")
                    elif vuln_type == 'Path_Traversal' and size > 75000:
                        print(f"      🎯 حجم كبير - قد يحتوي على محتوى ملفات النظام")
                    elif vuln_type == 'Command_Injection' and size > 85000:
                        print(f"      🎯 حجم كبير - قد يحتوي على نتائج الأوامر")
            else:
                print(f"      ❌ خطأ من السيرفر: {result.get('error', 'Unknown')}")
                
            return result
        else:
            return {
                'success': False,
                'error': f'HTTP {response.status_code}: {response.text[:100]}'
            }
            
    except Exception as e:
        return {
            'success': False,
            'error': str(e)
        }

def analyze_vulnerability_impact(vuln, results):
    """تحليل شامل لتأثير الثغرة"""
    
    print(f"\n📊 تحليل تأثير ثغرة {vuln['type']}:")
    print("-" * 50)
    
    if all(stage in results and results[stage] > 0 for stage in ['before', 'during', 'after']):
        before = results['before']
        during = results['during']
        after = results['after']
        
        print(f"📸 قبل: {before:,} bytes")
        print(f"📸 أثناء: {during:,} bytes")
        print(f"📸 بعد: {after:,} bytes")
        
        # تحليل التغييرات
        during_change = during - before
        after_change = after - during
        total_change = after - before
        
        print(f"\n📈 التغييرات:")
        print(f"   قبل → أثناء: {during_change:+,} bytes")
        print(f"   أثناء → بعد: {after_change:+,} bytes")
        print(f"   إجمالي: {total_change:+,} bytes")
        
        # تقييم جودة الدليل
        print(f"\n🎯 تقييم جودة الدليل:")
        
        if vuln['type'] == 'XSS':
            if after > 80000:
                print(f"   🎉 ممتاز - يحتوي على JavaScript وتأثيرات بصرية")
            elif after > 60000:
                print(f"   ✅ جيد - يحتوي على تأثيرات واضحة")
            else:
                print(f"   ⚠️ ضعيف - تأثيرات محدودة")
                
        elif vuln['type'] == 'SQL_Injection':
            if after > 70000:
                print(f"   🎉 ممتاز - يحتوي على بيانات قاعدة البيانات")
            elif after > 55000:
                print(f"   ✅ جيد - يحتوي على معلومات SQL")
            else:
                print(f"   ⚠️ ضعيف - لا يُظهر بيانات واضحة")
                
        elif vuln['type'] == 'Path_Traversal':
            if after > 75000:
                print(f"   🎉 ممتاز - يحتوي على محتوى ملفات النظام")
            elif after > 60000:
                print(f"   ✅ جيد - يحتوي على معلومات ملفات")
            else:
                print(f"   ⚠️ ضعيف - لا يُظهر محتوى ملفات واضح")
                
        elif vuln['type'] == 'Command_Injection':
            if after > 85000:
                print(f"   🎉 ممتاز - يحتوي على نتائج أوامر متعددة")
            elif after > 65000:
                print(f"   ✅ جيد - يحتوي على نتائج أوامر")
            else:
                print(f"   ⚠️ ضعيف - لا يُظهر نتائج أوامر واضحة")
        
        # تحديد المشاكل
        if before == during == after:
            print(f"   🚨 مشكلة كبيرة: جميع الصور متشابهة!")
        elif during == after:
            print(f"   🚨 مشكلة: لا توجد تأثيرات إضافية في 'بعد الاستغلال'")
        elif total_change < 5000:
            print(f"   ⚠️ تحذير: التغييرات قليلة جداً")
        else:
            print(f"   ✅ النتائج تبدو جيدة")
            
    else:
        print(f"❌ لم يتم التقاط جميع الصور بنجاح")

if __name__ == "__main__":
    test_real_vulnerability_impacts()
