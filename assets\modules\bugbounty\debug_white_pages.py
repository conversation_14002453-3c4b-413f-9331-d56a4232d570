#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
تشخيص مشكلة الصفحات البيضاء في صور الاستغلال
"""

import requests
import json
import time
from pathlib import Path

def debug_white_pages():
    """تشخيص مشكلة الصفحات البيضاء"""
    
    print("🔍 تشخيص مشكلة الصفحات البيضاء في صور الاستغلال")
    print("=" * 60)
    
    # اختبار URLs مختلفة
    test_urls = [
        {
            'name': 'الموقع الأصلي',
            'url': 'http://testphp.vulnweb.com/search.php',
            'expected': 'صفحة طبيعية'
        },
        {
            'name': 'XSS بسيط',
            'url': 'http://testphp.vulnweb.com/search.php?searchFor=<script>alert("test")</script>',
            'expected': 'صفحة مع XSS'
        },
        {
            'name': 'SQL بسيط',
            'url': 'http://testphp.vulnweb.com/artists.php?artist=\' OR \'1\'=\'1\' --',
            'expected': 'صفحة مع SQL'
        },
        {
            'name': 'معامل غير موجود',
            'url': 'http://testphp.vulnweb.com/search.php?nonexistent=test',
            'expected': 'صفحة طبيعية'
        }
    ]
    
    for test in test_urls:
        print(f"\n🧪 اختبار: {test['name']}")
        print(f"🔗 URL: {test['url']}")
        
        # اختبار الوصول المباشر
        test_direct_access(test['url'])
        
        # اختبار التقاط الصورة
        test_screenshot_capture(test['url'], test['name'])

def test_direct_access(url):
    """اختبار الوصول المباشر للURL"""
    
    try:
        print("📡 اختبار الوصول المباشر...")
        
        response = requests.get(url, timeout=10)
        
        print(f"   📊 Status Code: {response.status_code}")
        print(f"   📏 Content Length: {len(response.content):,} bytes")
        print(f"   📄 Content Type: {response.headers.get('content-type', 'غير محدد')}")
        
        # فحص المحتوى
        content = response.text[:500]  # أول 500 حرف
        
        if '<html' in content.lower():
            print("   ✅ يحتوي على HTML صحيح")
        else:
            print("   ❌ لا يحتوي على HTML صحيح")
            
        if 'error' in content.lower():
            print("   ⚠️ يحتوي على رسالة خطأ")
            
        if len(content.strip()) < 100:
            print("   ⚠️ محتوى قصير جداً - قد يكون صفحة فارغة")
            print(f"   📝 المحتوى: {content[:100]}")
            
    except Exception as e:
        print(f"   ❌ خطأ في الوصول: {e}")

def test_screenshot_capture(url, name):
    """اختبار التقاط الصورة"""
    
    try:
        print("📸 اختبار التقاط الصورة...")
        
        data = {
            'url': url,
            'filename': f'debug_{name.replace(" ", "_")}',
            'report_id': f'debug_{int(time.time())}',
            'vulnerability_name': 'Debug Test',
            'vulnerability_type': 'Debug',
            'stage': 'debug',
            'payload_data': None,
            'target_parameter': None
        }
        
        response = requests.post(
            'http://localhost:8000/v4_website',
            json=data,
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                size = result.get('file_size', 0)
                print(f"   ✅ نجح التقاط الصورة: {size:,} bytes")
                
                # تحليل حجم الصورة
                if size < 10000:
                    print("   ⚠️ حجم صغير - قد تكون صفحة بيضاء أو خطأ")
                elif size > 50000:
                    print("   ✅ حجم طبيعي - صفحة تحتوي على محتوى")
                else:
                    print("   📊 حجم متوسط - قد تكون صفحة بسيطة")
                    
            else:
                print(f"   ❌ فشل التقاط الصورة: {result.get('error', 'خطأ غير محدد')}")
        else:
            print(f"   ❌ خطأ HTTP: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ خطأ في التقاط الصورة: {e}")

def analyze_screenshot_sizes():
    """تحليل أحجام الصور الموجودة"""
    
    print(f"\n📊 تحليل أحجام الصور الموجودة:")
    
    screenshots_dir = Path("screenshots")
    
    if not screenshots_dir.exists():
        print("❌ مجلد الصور غير موجود")
        return
        
    # تجميع الصور حسب الحجم
    size_groups = {}
    
    for file in screenshots_dir.rglob("*.png"):
        size = file.stat().st_size
        
        if size not in size_groups:
            size_groups[size] = []
        size_groups[size].append(file.name)
    
    # عرض التجميعات
    for size, files in sorted(size_groups.items()):
        print(f"\n📏 حجم {size:,} bytes ({len(files)} ملف):")
        
        # تحليل نوع الحجم
        if size < 10000:
            print("   ⚠️ حجم صغير - قد تكون صفحات بيضاء أو أخطاء")
        elif size > 50000:
            print("   ✅ حجم كبير - صفحات تحتوي على محتوى")
        else:
            print("   📊 حجم متوسط - صفحات بسيطة")
            
        # عرض أسماء الملفات
        for file in files[:5]:  # أول 5 ملفات فقط
            print(f"     📸 {file}")
        
        if len(files) > 5:
            print(f"     ... و {len(files) - 5} ملف آخر")

def test_specific_payloads():
    """اختبار payloads محددة لمعرفة سبب الصفحات البيضاء"""
    
    print(f"\n🧪 اختبار payloads محددة:")
    
    payloads = [
        {
            'name': 'XSS عادي',
            'url': 'http://testphp.vulnweb.com/search.php',
            'param': 'searchFor',
            'payload': '<script>alert("test")</script>'
        },
        {
            'name': 'XSS مُرمز',
            'url': 'http://testphp.vulnweb.com/search.php',
            'param': 'searchFor', 
            'payload': '%3Cscript%3Ealert%28%22test%22%29%3C%2Fscript%3E'
        },
        {
            'name': 'نص عادي',
            'url': 'http://testphp.vulnweb.com/search.php',
            'param': 'searchFor',
            'payload': 'test_normal_text'
        }
    ]
    
    for test in payloads:
        print(f"\n🔍 اختبار {test['name']}:")
        
        # بناء URL
        full_url = f"{test['url']}?{test['param']}={test['payload']}"
        print(f"🔗 {full_url}")
        
        # اختبار الوصول
        test_direct_access(full_url)

if __name__ == "__main__":
    debug_white_pages()
    analyze_screenshot_sizes()
    test_specific_payloads()
