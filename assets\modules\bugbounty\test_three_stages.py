#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json
import time

def test_three_stages():
    """اختبار الصور الثلاث: قبل/أثناء/بعد الاستغلال"""
    
    print("🔥 اختبار الصور الثلاث: قبل/أثناء/بعد الاستغلال")
    print("🎯 لنرى الفرق الحقيقي في محتوى الصور")
    print("=" * 60)
    
    report_id = f"three_stages_{int(time.time())}"
    
    # بيانات XSS
    vuln_data = {
        'url': 'http://testphp.vulnweb.com/search.php',
        'vulnerability_name': 'XSS Cross Site Scripting',
        'vulnerability_type': 'XSS',
        'payload_data': '<script>alert("XSS_TEST")</script>',
        'target_parameter': 'searchFor'
    }
    
    print(f"🎯 اختبار XSS على: {vuln_data['url']}")
    print(f"💉 Payload: {vuln_data['payload_data']}")
    print(f"📝 المعامل: {vuln_data['target_parameter']}")
    
    # التقاط الصور الثلاث
    stages = ['before', 'during', 'after']
    results = {}
    
    for stage in stages:
        print(f"\n📸 التقاط صورة {stage}...")
        
        data = {
            'url': vuln_data['url'],
            'filename': f'{stage}_XSS_REAL_TEST',
            'report_id': report_id,
            'vulnerability_name': vuln_data['vulnerability_name'],
            'vulnerability_type': vuln_data['vulnerability_type'],
            'stage': stage,
            'payload_data': vuln_data['payload_data'],
            'target_parameter': vuln_data['target_parameter']
        }
        
        result = capture_screenshot_detailed(data, stage)
        
        if result['success']:
            size = result.get('file_size', 0)
            path = result.get('file_path', 'Unknown')
            results[stage] = {'size': size, 'path': path}
            
            print(f"   ✅ نجح: {size:,} bytes")
            print(f"   📁 المسار: {path}")
            
            # تحليل فوري
            if stage == 'before':
                print(f"   📊 صورة الموقع الأصلي")
            elif stage == 'during':
                before_size = results.get('before', {}).get('size', 0)
                if size != before_size:
                    print(f"   🔄 تغيير في المحتوى - payload مُطبق")
                else:
                    print(f"   ⚠️ لا يوجد تغيير واضح")
            elif stage == 'after':
                during_size = results.get('during', {}).get('size', 0)
                if size != during_size:
                    print(f"   🎉 تأثيرات إضافية - مختلف عن 'أثناء'")
                else:
                    print(f"   ❌ نفس محتوى 'أثناء' - لا توجد تأثيرات إضافية")
        else:
            print(f"   ❌ فشل: {result.get('error', 'خطأ غير محدد')}")
            results[stage] = {'size': 0, 'path': None}
    
    # تحليل شامل للنتائج
    analyze_three_stages_results(results)

def capture_screenshot_detailed(data, stage):
    """التقاط صورة مع تفاصيل كاملة"""
    
    try:
        print(f"      🔍 إرسال طلب للسيرفر...")
        print(f"      🎯 المرحلة: {stage}")
        
        if stage == 'before':
            print(f"      📊 URL أصلي: {data['url']}")
        else:
            print(f"      💉 مع Payload: {data.get('payload_data', 'None')}")
        
        response = requests.post(
            'http://localhost:8000/v4_website',
            json=data,
            timeout=60
        )
        
        if response.status_code == 200:
            result = response.json()
            
            if result.get('success'):
                print(f"      ✅ استجابة ناجحة من السيرفر")
            else:
                print(f"      ❌ خطأ من السيرفر: {result.get('error', 'Unknown')}")
                
            return result
        else:
            return {
                'success': False,
                'error': f'HTTP {response.status_code}: {response.text[:100]}'
            }
            
    except Exception as e:
        return {
            'success': False,
            'error': str(e)
        }

def analyze_three_stages_results(results):
    """تحليل شامل لنتائج الصور الثلاث"""
    
    print(f"\n📊 تحليل شامل للصور الثلاث:")
    print("=" * 50)
    
    if all(stage in results and results[stage]['size'] > 0 for stage in ['before', 'during', 'after']):
        before_size = results['before']['size']
        during_size = results['during']['size']
        after_size = results['after']['size']
        
        print(f"📸 قبل الاستغلال: {before_size:,} bytes")
        print(f"📸 أثناء الاستغلال: {during_size:,} bytes")
        print(f"📸 بعد الاستغلال: {after_size:,} bytes")
        
        # تحليل التغييرات
        before_during_diff = abs(during_size - before_size)
        during_after_diff = abs(after_size - during_size)
        
        print(f"\n📈 التغييرات:")
        print(f"   قبل → أثناء: {before_during_diff:,} bytes")
        print(f"   أثناء → بعد: {during_after_diff:,} bytes")
        
        # تقييم النتائج
        print(f"\n🎯 تقييم النتائج:")
        
        if before_during_diff == 0:
            print(f"   ❌ لا يوجد فرق بين 'قبل' و 'أثناء' - payload لا يعمل")
        else:
            print(f"   ✅ يوجد فرق بين 'قبل' و 'أثناء' - payload يعمل")
            
        if during_after_diff == 0:
            print(f"   ❌ لا يوجد فرق بين 'أثناء' و 'بعد' - نفس المحتوى!")
        else:
            print(f"   ✅ يوجد فرق بين 'أثناء' و 'بعد' - تأثيرات إضافية")
            
        # تحديد المشاكل
        print(f"\n⚠️ المشاكل المكتشفة:")
        
        if before_size == during_size == after_size:
            print(f"   🚨 جميع الصور متشابهة - النظام لا يعمل!")
        elif during_size == after_size:
            print(f"   🚨 صور 'أثناء' و 'بعد' متشابهة - لا توجد تأثيرات إضافية!")
        elif after_size > 200000:
            print(f"   🚨 صورة 'بعد' كبيرة جداً - قد تكون ألوان فقط!")
        else:
            print(f"   ✅ الصور تبدو طبيعية")
            
        # عرض مسارات الصور للفحص اليدوي
        print(f"\n📁 مسارات الصور للفحص اليدوي:")
        for stage in ['before', 'during', 'after']:
            if results[stage]['path']:
                print(f"   {stage}: {results[stage]['path']}")
                
    else:
        print(f"❌ لم يتم التقاط جميع الصور بنجاح")

if __name__ == "__main__":
    test_three_stages()
