<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إصلاح صور بعد الاستغلال</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success { background: #d4edda; border-color: #c3e6cb; }
        .error { background: #f8d7da; border-color: #f5c6cb; }
        .info { background: #d1ecf1; border-color: #bee5eb; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        #results { margin-top: 20px; }
        .result-item {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 اختبار إصلاح صور بعد الاستغلال</h1>
        
        <div class="test-section info">
            <h3>📋 الهدف من الاختبار</h3>
            <p>التأكد من أن صور "بعد الاستغلال" تُظهر الموقع الحقيقي مع payload بدلاً من صفحة HTML ملونة مُولدة.</p>
        </div>

        <div class="test-section">
            <h3>🧪 اختبارات الإصلاح</h3>
            <button onclick="testCreateRealExploitedUrl()">اختبار إنشاء URL حقيقي</button>
            <button onclick="testXSSExploitation()">اختبار XSS</button>
            <button onclick="testSQLInjection()">اختبار SQL Injection</button>
            <button onclick="testPythonScreenshot()">اختبار سيرفر Python</button>
        </div>

        <div id="results"></div>
    </div>

    <script src="BugBountyCore.js"></script>
    <script>
        function addResult(message, type = 'info') {
            const resultsDiv = document.getElementById('results');
            const resultItem = document.createElement('div');
            resultItem.className = `result-item ${type}`;
            resultItem.innerHTML = `<strong>${new Date().toLocaleTimeString()}:</strong> ${message}`;
            resultsDiv.appendChild(resultItem);
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }

        async function testCreateRealExploitedUrl() {
            addResult('🧪 بدء اختبار إنشاء URL حقيقي...', 'info');
            
            try {
                // إنشاء مثيل من BugBountyCore
                const bugBountyCore = new BugBountyCore();
                
                // إنشاء ثغرة اختبار
                const testVuln = {
                    name: 'XSS Test',
                    payload: '<script>alert("test")</script>',
                    parameter: 'search'
                };
                
                const targetUrl = 'http://testphp.vulnweb.com/search.php';
                
                // اختبار الدالة الجديدة
                const realUrl = await bugBountyCore.createRealExploitedUrl(testVuln, targetUrl);
                
                if (realUrl.includes('testphp.vulnweb.com') && !realUrl.startsWith('data:text/html')) {
                    addResult(`✅ نجح: تم إنشاء URL حقيقي: ${realUrl}`, 'success');
                } else {
                    addResult(`❌ فشل: URL غير صحيح: ${realUrl}`, 'error');
                }
                
            } catch (error) {
                addResult(`❌ خطأ في الاختبار: ${error.message}`, 'error');
            }
        }

        async function testXSSExploitation() {
            addResult('🧪 بدء اختبار XSS...', 'info');
            
            try {
                const bugBountyCore = new BugBountyCore();
                
                const xssVuln = {
                    name: 'XSS Cross Site Scripting',
                    payload: '<script>alert("XSS")</script>',
                    parameter: 'q'
                };
                
                const targetUrl = 'http://testphp.vulnweb.com/search.php';
                const exploitedUrl = await bugBountyCore.createRealExploitedUrl(xssVuln, targetUrl);
                
                if (exploitedUrl.includes('script') && exploitedUrl.includes('testphp.vulnweb.com')) {
                    addResult(`✅ XSS URL صحيح: ${exploitedUrl}`, 'success');
                } else {
                    addResult(`❌ XSS URL خاطئ: ${exploitedUrl}`, 'error');
                }
                
            } catch (error) {
                addResult(`❌ خطأ في اختبار XSS: ${error.message}`, 'error');
            }
        }

        async function testSQLInjection() {
            addResult('🧪 بدء اختبار SQL Injection...', 'info');
            
            try {
                const bugBountyCore = new BugBountyCore();
                
                const sqlVuln = {
                    name: 'SQL Injection',
                    payload: "' OR '1'='1' --",
                    parameter: 'id'
                };
                
                const targetUrl = 'http://testphp.vulnweb.com/artists.php';
                const exploitedUrl = await bugBountyCore.createRealExploitedUrl(sqlVuln, targetUrl);
                
                if (exploitedUrl.includes('OR') && exploitedUrl.includes('testphp.vulnweb.com')) {
                    addResult(`✅ SQL URL صحيح: ${exploitedUrl}`, 'success');
                } else {
                    addResult(`❌ SQL URL خاطئ: ${exploitedUrl}`, 'error');
                }
                
            } catch (error) {
                addResult(`❌ خطأ في اختبار SQL: ${error.message}`, 'error');
            }
        }

        async function testPythonScreenshot() {
            addResult('🧪 بدء اختبار سيرفر Python...', 'info');
            
            try {
                // اختبار الاتصال بسيرفر Python
                const response = await fetch('http://localhost:8000/health');
                
                if (response.ok) {
                    const data = await response.json();
                    addResult(`✅ سيرفر Python يعمل: ${data.status}`, 'success');
                    
                    // اختبار التقاط صورة
                    const testData = {
                        url: 'http://testphp.vulnweb.com/search.php?q=test_payload',
                        filename: 'test_after_fix',
                        report_id: 'test_fix_123',
                        vulnerability_name: 'XSS Test',
                        vulnerability_type: 'Cross-Site Scripting',
                        stage: 'after',
                        payload_data: '<script>alert("test")</script>',
                        target_parameter: 'q'
                    };
                    
                    const screenshotResponse = await fetch('http://localhost:8000/v4_website', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify(testData)
                    });
                    
                    if (screenshotResponse.ok) {
                        const result = await screenshotResponse.json();
                        addResult(`✅ تم التقاط صورة بنجاح: ${result.filename}`, 'success');
                    } else {
                        addResult(`❌ فشل في التقاط الصورة: ${screenshotResponse.status}`, 'error');
                    }
                    
                } else {
                    addResult(`❌ سيرفر Python لا يعمل: ${response.status}`, 'error');
                }
                
            } catch (error) {
                addResult(`❌ خطأ في اختبار Python: ${error.message}`, 'error');
            }
        }

        // تشغيل اختبار تلقائي عند تحميل الصفحة
        window.onload = function() {
            addResult('🚀 تم تحميل صفحة الاختبار', 'info');
            addResult('📋 اضغط على الأزرار لتشغيل الاختبارات', 'info');
        };
    </script>
</body>
</html>
