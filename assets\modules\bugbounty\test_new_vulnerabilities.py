#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار ثغرات جديدة غير موجودة في سيرفر Python
لنرى كيف يتعامل النظام تلقائياً وديناميكياً مع ثغرات غير مُبرمجة
"""

import requests
import json
import time
import os
from pathlib import Path

def test_new_vulnerabilities():
    """اختبار ثغرات جديدة غير موجودة في سيرفر Python"""
    
    print("🔥 اختبار ثغرات جديدة غير موجودة في سيرفر Python")
    print("🎯 لنرى كيف يتعامل النظام تلقائياً وديناميكياً معها")
    print("=" * 80)
    
    # التحقق من سيرفر Python
    try:
        response = requests.get('http://localhost:8000/health', timeout=5)
        if response.status_code == 200:
            print("✅ سيرفر Python يعمل بنجاح")
        else:
            print(f"❌ سيرفر Python لا يعمل: {response.status_code}")
            return
    except Exception as e:
        print(f"❌ خطأ في الاتصال بسيرفر Python: {e}")
        return
    
    # 🔥 ثغرات جديدة ليست مُبرمجة في سيرفر Python
    new_vulnerabilities = [
        {
            'name': 'Path Traversal Attack',
            'type': 'Path_Traversal',
            'url': 'http://testphp.vulnweb.com/search.php',
            'parameter': 'file',
            'payload': '../../../etc/passwd'
        },
        {
            'name': 'Open Redirect Vulnerability',
            'type': 'Open_Redirect',
            'url': 'http://testphp.vulnweb.com/search.php',
            'parameter': 'redirect',
            'payload': 'http://evil.com'
        },
        {
            'name': 'LDAP Injection Attack',
            'type': 'LDAP_Injection',
            'url': 'http://testphp.vulnweb.com/search.php',
            'parameter': 'username',
            'payload': '*)(uid=*))(|(uid=*'
        },
        {
            'name': 'XPath Injection Vulnerability',
            'type': 'XPath_Injection',
            'url': 'http://testphp.vulnweb.com/search.php',
            'parameter': 'query',
            'payload': '\' or \'1\'=\'1\' or \'\'=\''
        },
        {
            'name': 'Template Injection (SSTI)',
            'type': 'Template_Injection',
            'url': 'http://testphp.vulnweb.com/search.php',
            'parameter': 'template',
            'payload': '{{7*7}}'
        },
        {
            'name': 'Business Logic Flaws',
            'type': 'Business_Logic',
            'url': 'http://testphp.vulnweb.com/search.php',
            'parameter': 'amount',
            'payload': '-100'
        },
        {
            'name': 'Authentication Bypass',
            'type': 'Auth_Bypass',
            'url': 'http://testphp.vulnweb.com/search.php',
            'parameter': 'user',
            'payload': 'admin\' --'
        },
        {
            'name': 'Information Disclosure',
            'type': 'Info_Disclosure',
            'url': 'http://testphp.vulnweb.com/search.php',
            'parameter': 'debug',
            'payload': 'true'
        }
    ]
    
    print(f"\n🧪 اختبار {len(new_vulnerabilities)} ثغرة جديدة غير مُبرمجة...")
    
    for i, vuln in enumerate(new_vulnerabilities, 1):
        print(f"\n{'='*70}")
        print(f"🎯 اختبار {i}/{len(new_vulnerabilities)}: {vuln['name']}")
        print(f"🔍 نوع: {vuln['type']} (غير موجود في سيرفر Python)")
        print(f"{'='*70}")
        
        test_unknown_vulnerability(vuln)
        
        # انتظار قصير بين الاختبارات
        time.sleep(2)
    
    print(f"\n🎉 تم الانتهاء من اختبار الثغرات الجديدة!")
    print("📊 تحقق من كيف تعامل سيرفر Python مع الثغرات غير المعروفة...")
    
    # تحليل النتائج
    analyze_unknown_vulnerability_results()

def test_unknown_vulnerability(vuln):
    """اختبار ثغرة غير معروفة لسيرفر Python"""
    
    report_id = f"unknown_{vuln['type']}_{int(time.time())}"
    
    print(f"🔗 الموقع: {vuln['url']}")
    print(f"🎯 نوع الثغرة: {vuln['type']} (جديد)")
    print(f"📝 المعامل: {vuln['parameter']}")
    print(f"💉 Payload: {vuln['payload']}")
    
    # اختبار كيف يتعامل سيرفر Python مع الثغرة الجديدة
    stages = ['before', 'during', 'after']
    results = {}
    
    for stage in stages:
        print(f"\n📸 التقاط صورة {stage} للثغرة الجديدة...")
        
        result = capture_unknown_vulnerability_screenshot(
            url=vuln['url'],
            filename=f"{stage}_{vuln['type']}_NEW",
            report_id=report_id,
            vulnerability_name=vuln['name'],
            vulnerability_type=vuln['type'],  # نوع جديد غير معروف
            stage=stage,
            payload_data=vuln['payload'],
            target_parameter=vuln['parameter']
        )
        
        if result['success']:
            size = result.get('file_size', 0)
            results[stage] = size
            print(f"   ✅ نجح: {size:,} bytes")
            
            # تحليل كيف تعامل السيرفر مع الثغرة الجديدة
            if stage == 'before':
                print(f"   📊 صورة الموقع الأصلي")
            elif stage == 'during':
                if size != results.get('before', 0):
                    print(f"   🔄 السيرفر طبق payload للثغرة الجديدة!")
                else:
                    print(f"   ⚠️ السيرفر لم يتعرف على الثغرة الجديدة")
            elif stage == 'after':
                if size != results.get('during', 0):
                    print(f"   🎉 السيرفر أنشأ تأثيرات للثغرة الجديدة تلقائياً!")
                else:
                    print(f"   📊 السيرفر استخدم النظام الافتراضي للثغرة الجديدة")
        else:
            print(f"   ❌ فشل: {result.get('error', 'خطأ غير محدد')}")
            results[stage] = 0
    
    # تحليل كيف تعامل السيرفر مع الثغرة الجديدة
    analyze_server_response_to_new_vulnerability(vuln['type'], results)

def analyze_server_response_to_new_vulnerability(vuln_type, results):
    """تحليل كيف تعامل السيرفر مع الثغرة الجديدة"""
    
    print(f"\n📊 تحليل استجابة السيرفر للثغرة الجديدة {vuln_type}:")
    
    if all(stage in results for stage in ['before', 'during', 'after']):
        before = results['before']
        during = results['during']
        after = results['after']
        
        print(f"   📸 قبل: {before:,} bytes")
        print(f"   📸 أثناء: {during:,} bytes")
        print(f"   📸 بعد: {after:,} bytes")
        
        # تحليل استجابة السيرفر
        print(f"\n   🤖 استجابة السيرفر للثغرة الجديدة:")
        
        if during == before and after == during:
            print(f"      ❌ السيرفر لم يتعرف على الثغرة - استخدم URL الأصلي فقط")
        elif during != before and after == during:
            print(f"      📊 السيرفر طبق payload لكن بدون تأثيرات إضافية")
        elif during != before and after != during:
            print(f"      🎉 السيرفر تعامل تلقائياً مع الثغرة الجديدة!")
            print(f"      ✅ النظام الديناميكي يعمل بنجاح!")
        else:
            print(f"      🔍 استجابة غير متوقعة - يحتاج تحليل إضافي")
            
        # تقييم النظام الديناميكي
        if during != before or after != during:
            print(f"      🏆 النظام الديناميكي نجح في التعامل مع ثغرة جديدة!")
        else:
            print(f"      ⚠️ النظام الديناميكي لم يتعامل مع الثغرة الجديدة")
            
    else:
        print(f"   ❌ لم يتم التقاط جميع الصور بنجاح")

def capture_unknown_vulnerability_screenshot(url, filename, report_id, vulnerability_name, vulnerability_type, stage, payload_data=None, target_parameter=None):
    """التقاط صورة لثغرة غير معروفة"""
    
    try:
        data = {
            'url': url,
            'filename': filename,
            'report_id': report_id,
            'vulnerability_name': vulnerability_name,
            'vulnerability_type': vulnerability_type,  # نوع جديد غير معروف
            'stage': stage,
            'payload_data': payload_data,
            'target_parameter': target_parameter
        }
        
        print(f"      🔍 إرسال ثغرة جديدة للسيرفر: {vulnerability_type}")
        
        response = requests.post(
            'http://localhost:8000/v4_website',
            json=data,
            timeout=30
        )
        
        if response.status_code == 200:
            return response.json()
        else:
            return {
                'success': False,
                'error': f'HTTP {response.status_code}: {response.text[:100]}'
            }
            
    except Exception as e:
        return {
            'success': False,
            'error': str(e)
        }

def analyze_unknown_vulnerability_results():
    """تحليل شامل لنتائج الثغرات الجديدة"""
    
    print(f"\n📊 تحليل شامل لاستجابة السيرفر للثغرات الجديدة:")
    print("=" * 70)
    
    screenshots_dir = Path("screenshots")
    
    if not screenshots_dir.exists():
        print("❌ مجلد الصور غير موجود")
        return
    
    # البحث عن صور الثغرات الجديدة
    new_files = list(screenshots_dir.rglob("*NEW*.png"))
    
    if not new_files:
        print("❌ لم يتم العثور على صور الثغرات الجديدة")
        return
    
    print(f"✅ تم العثور على {len(new_files)} صورة للثغرات الجديدة")
    
    # تجميع حسب نوع الثغرة
    vuln_groups = {}
    
    for file in new_files:
        # استخراج نوع الثغرة من اسم الملف
        for vuln_type in ['Path_Traversal', 'Open_Redirect', 'LDAP_Injection', 'XPath_Injection', 'Template_Injection', 'Business_Logic', 'Auth_Bypass', 'Info_Disclosure']:
            if vuln_type in file.name:
                if vuln_type not in vuln_groups:
                    vuln_groups[vuln_type] = []
                vuln_groups[vuln_type].append(file)
                break
    
    # عرض النتائج لكل ثغرة جديدة
    successful_adaptations = 0
    total_vulnerabilities = len(vuln_groups)
    
    for vuln_type, files in vuln_groups.items():
        print(f"\n🎯 {vuln_type} (ثغرة جديدة - {len(files)} صورة):")
        
        stages = {'before': None, 'during': None, 'after': None}
        
        for file in files:
            size = file.stat().st_size
            
            for stage in stages.keys():
                if stage in file.name:
                    stages[stage] = size
                    print(f"   📸 {stage}: {size:,} bytes")
                    break
        
        # تحليل استجابة السيرفر
        if all(stages.values()):
            before, during, after = stages['before'], stages['during'], stages['after']
            
            if during != before or after != during:
                print(f"   🎉 السيرفر تكيف مع الثغرة الجديدة!")
                successful_adaptations += 1
            else:
                print(f"   ❌ السيرفر لم يتكيف مع الثغرة الجديدة")
    
    # تقييم عام للنظام الديناميكي
    print(f"\n🏆 تقييم عام للنظام الديناميكي:")
    print(f"   📊 الثغرات المختبرة: {total_vulnerabilities}")
    print(f"   ✅ الثغرات التي تكيف معها السيرفر: {successful_adaptations}")
    print(f"   📈 معدل النجاح: {(successful_adaptations/total_vulnerabilities)*100:.1f}%")
    
    if successful_adaptations == total_vulnerabilities:
        print(f"   🏆 ممتاز! النظام الديناميكي يتعامل مع جميع الثغرات الجديدة!")
    elif successful_adaptations > total_vulnerabilities // 2:
        print(f"   👍 جيد! النظام الديناميكي يتعامل مع معظم الثغرات الجديدة")
    else:
        print(f"   ⚠️ النظام الديناميكي يحتاج تحسين للثغرات الجديدة")

if __name__ == "__main__":
    test_new_vulnerabilities()
