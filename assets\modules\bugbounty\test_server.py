#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار سيرفر Python Screenshot Service
"""

import requests
import json
import time

def test_health():
    """اختبار /health endpoint"""
    try:
        print("🔍 اختبار /health...")
        response = requests.get('http://localhost:8000/health', timeout=5)
        print(f"📊 Status Code: {response.status_code}")
        print(f"📄 Response: {response.text}")
        return response.status_code == 200
    except Exception as e:
        print(f"❌ خطأ في /health: {e}")
        return False

def test_v4_website():
    """اختبار /v4_website endpoint"""
    try:
        print("\n🔍 اختبار /v4_website...")
        
        test_data = {
            "url": "https://httpbin.org/get",
            "filename": "test_screenshot",
            "report_id": f"test_report_{int(time.time())}",
            "vulnerability_name": "Test Vulnerability",
            "vulnerability_type": "XSS",
            "stage": "before",
            "payload_data": "<script>alert('test')</script>",
            "target_parameter": "q"
        }
        
        response = requests.post(
            'http://localhost:8000/v4_website',
            json=test_data,
            timeout=30
        )
        
        print(f"📊 Status Code: {response.status_code}")
        print(f"📄 Response: {response.text[:500]}...")
        
        return response.status_code == 200
        
    except Exception as e:
        print(f"❌ خطأ في /v4_website: {e}")
        return False

def main():
    """تشغيل جميع الاختبارات"""
    print("🚀 بدء اختبار سيرفر Python Screenshot Service")
    print("=" * 50)
    
    # اختبار الصحة
    health_ok = test_health()
    
    # اختبار التقاط الصور
    if health_ok:
        screenshot_ok = test_v4_website()
    else:
        print("⚠️ تخطي اختبار التقاط الصور بسبب فشل اختبار الصحة")
        screenshot_ok = False
    
    print("\n" + "=" * 50)
    print("📊 نتائج الاختبارات:")
    print(f"✅ Health Check: {'نجح' if health_ok else 'فشل'}")
    print(f"✅ Screenshot Test: {'نجح' if screenshot_ok else 'فشل'}")
    
    if health_ok and screenshot_ok:
        print("🎉 جميع الاختبارات نجحت!")
    else:
        print("❌ بعض الاختبارات فشلت")

if __name__ == "__main__":
    main()
