#!/usr/bin/env python3
"""
خدمة ويب Python لالتقاط الصور الحقيقية للمواقع
تستخدم Selenium و Playwright لالتقاط صور عالية الجودة
"""

import os
import sys
import json
import base64
import asyncio
import logging
import time
import random
import requests
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional
import threading
import queue
from collections import defaultdict

# 🔥 نظام تتبع الصفحات لضمان معالجة كل صفحة في وقتها
current_page_being_processed = None
page_processing_lock = False
processed_pages = set()
current_page_vulnerabilities = []
page_processing_start_time = None

def get_page_identifier(url):
    """استخراج معرف الصفحة من URL"""
    if '/login' in url:
        return 'login'
    elif '/search' in url:
        return 'search'
    elif '/admin' in url:
        return 'admin'
    else:
        return 'main'

def is_new_page_request(url):
    """التحقق من أن هذا طلب لصفحة جديدة"""
    global current_page_being_processed
    page_id = get_page_identifier(url)

    if current_page_being_processed is None:
        current_page_being_processed = page_id
        print(f"🔥 بدء معالجة الصفحة الجديدة: {page_id}")
        return True
    elif current_page_being_processed != page_id:
        print(f"🔥 انتقال من الصفحة {current_page_being_processed} إلى {page_id}")
        processed_pages.add(current_page_being_processed)
        current_page_being_processed = page_id
        return True
    else:
        print(f"🔄 استمرار معالجة نفس الصفحة: {page_id}")
        return False


# Flask للخدمة الويب
try:
    from flask import Flask, request, jsonify, send_file
    from flask_cors import CORS
except ImportError:
    print("❌ Flask غير مثبت. قم بتثبيته: pip install flask flask-cors")
    sys.exit(1)

# Selenium للتقاط الصور
try:
    from selenium import webdriver
    from selenium.webdriver.chrome.options import Options as ChromeOptions
    from selenium.webdriver.common.by import By
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    from selenium.webdriver.chrome.service import Service as ChromeService
except ImportError:
    print("❌ Selenium غير مثبت. قم بتثبيته: pip install selenium")
    sys.exit(1)

# WebDriver Manager لتحميل ChromeDriver تلقائياً
try:
    from webdriver_manager.chrome import ChromeDriverManager
except ImportError:
    print("❌ WebDriver Manager غير مثبت. قم بتثبيته: pip install webdriver-manager")
    sys.exit(1)

# إعداد التسجيل
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)



# 🔥 استيراد الكلاس الصحيح من screenshot_service.py
try:
    from screenshot_service import ScreenshotService
    logger.info("✅ تم استيراد ScreenshotService من screenshot_service.py")
except ImportError:
    logger.error("❌ فشل في استيراد ScreenshotService من screenshot_service.py")
    # إنشاء كلاس بديل بسيط
    class ScreenshotService:
        def __init__(self):
            self.screenshots_dir = Path("./screenshots")
            self.screenshots_dir.mkdir(exist_ok=True)
            logger.warning("⚠️ استخدام ScreenshotService البديل")

# إنشاء تطبيق Flask
app = Flask(__name__)
CORS(app)  # السماح بطلبات CORS

# 🔥 نظام بسيط للمعالجة المتسلسلة صفحة بصفحة
page_locks = defaultdict(threading.Lock)  # قفل لكل صفحة

# 🔥 نظام queue للمعالجة المتسلسلة الحقيقية
request_queues = defaultdict(queue.Queue)  # queue لكل صفحة
processing_threads = {}  # threads للمعالجة

# 🔥 نظام منع الطلبات المتعددة المتزامنة - قفل عام واحد + queue
global_processing_lock = threading.Lock()
last_request_time = 0
MIN_REQUEST_INTERVAL = 0.5  # 🔥 إصلاح: 0.5 ثانية بين الطلبات بدلاً من 5 ثواني
request_queue = queue.Queue()  # queue للطلبات
processing_thread = None
is_processing = False

def start_processing_thread():
    """بدء thread معالجة الطلبات"""
    global processing_thread
    if processing_thread is None or not processing_thread.is_alive():
        processing_thread = threading.Thread(target=process_request_queue, daemon=True)
        processing_thread.start()
        logger.info("🚀 تم بدء thread معالجة الطلبات")

def process_request_queue():
    """معالجة queue الطلبات بشكل متسلسل"""
    global last_request_time, is_processing

    while True:
        try:
            # انتظار طلب جديد
            request_data = request_queue.get(timeout=1)
            if request_data is None:  # إشارة إيقاف
                break

            is_processing = True

            # انتظار إجباري بين الطلبات
            current_time = time.time()
            time_since_last = current_time - last_request_time

            if time_since_last < MIN_REQUEST_INTERVAL:
                wait_time = MIN_REQUEST_INTERVAL - time_since_last
                logger.info(f"⏳ انتظار {wait_time:.1f} ثانية قبل المعالجة...")
                time.sleep(wait_time)

            last_request_time = time.time()

            # معالجة الطلب
            process_single_request_new(request_data)

            # تحديد انتهاء المعالجة
            request_queue.task_done()
            is_processing = False

        except queue.Empty:
            continue
        except Exception as e:
            logger.error(f"❌ خطأ في معالجة queue: {e}")
            is_processing = False

def process_single_request_new(request_data):
    """معالجة طلب واحد"""
    try:
        url = request_data['url']
        filename = request_data['filename']
        report_id = request_data['report_id']
        vulnerability_name = request_data.get('vulnerability_name')
        vulnerability_type = request_data.get('vulnerability_type')
        stage = request_data.get('stage', 'screenshot')
        payload_data = request_data.get('payload_data')
        target_parameter = request_data.get('target_parameter')
        response_callback = request_data['response_callback']

        page_key = get_page_key(url)
        logger.info(f"🔒 بدء معالجة متسلسلة: {page_key} - {stage} - {vulnerability_name}")

        # 🔥 تعريف المتغيرات الحقيقية في البداية مع قيم افتراضية
        v4_data_received = request_data.get('v4_real_data', {})
        test_results_received = v4_data_received.get('test_results', [])
        exploitation_status_received = v4_data_received.get('exploitation_status', 'unknown')
        verification_proof_received = v4_data_received.get('verification_proof', [])
        response_data_received = v4_data_received.get('response_data', '')
        error_messages_received = v4_data_received.get('error_messages', [])
        success_indicators_received = v4_data_received.get('success_indicators', [])
        vulnerability_meta_received = v4_data_received.get('vulnerability_meta', {})

        # 🔥 البيانات الشاملة من الدوال الـ36
        real_exploitation_evidence = v4_data_received.get('real_exploitation_evidence', [])
        actual_response_content = v4_data_received.get('actual_response_content', '')
        exploitation_results = v4_data_received.get('exploitation_results', [])
        vulnerability_impact_data = v4_data_received.get('vulnerability_impact_data', '')

        real_test_results = test_results_received if isinstance(test_results_received, list) else []
        real_exploitation_status = exploitation_status_received
        real_verification_proof = verification_proof_received if isinstance(verification_proof_received, list) else []
        real_response_data = response_data_received
        real_error_messages = error_messages_received if isinstance(error_messages_received, list) else []
        real_success_indicators = success_indicators_received if isinstance(success_indicators_received, list) else []
        vulnerability_meta = vulnerability_meta_received

        # 🔥 النظام الجديد: استخراج اسم الثغرة من filename والتقاط الصور الثلاث تلقائياً
        extracted_vuln_name = vulnerability_name

        if not extracted_vuln_name and filename:
            # استخراج اسم الثغرة من filename مثل "before_XSS_Cross_Site_Scripting"
            parts = filename.split('_')
            if len(parts) >= 2 and parts[0] == 'before':
                extracted_vuln_name = '_'.join(parts[1:])  # كل شيء بعد "before_"
                logger.info(f"🔍 تم استخراج اسم الثغرة من filename: {extracted_vuln_name}")

        # 🔥 محاولة استخراج البيانات الحقيقية من النظام v4
        real_payload = payload_data
        real_exploitation_result = None

        # طباعة البيانات المُستلمة للتشخيص
        logger.info(f"📊 البيانات المُستلمة من النظام v4:")
        logger.info(f"   - vulnerability_name: {vulnerability_name}")
        logger.info(f"   - payload_data: {payload_data}")
        logger.info(f"   - target_parameter: {target_parameter}")
        logger.info(f"   - vulnerability_type: {vulnerability_type}")

        # إذا تم استخراج اسم ثغرة، التقط الصور الثلاث بالتسلسل مع الدالة الديناميكية الحقيقية
        if extracted_vuln_name:
            logger.info(f"🎯 بدء التقاط الصور الثلاث للثغرة بالتسلسل: {extracted_vuln_name}")

            # 🔥 استخدام الدالة الأصلية للصور الثلاث مع التأثيرات القوية
            logger.info(f"🔥 استخدام capture_vulnerability_sequence للثغرة: {extracted_vuln_name}")

            # 🔥 تمرير جميع البيانات الحقيقية من النظام v4
            v4_data = {
                'vulnerability_name': vulnerability_name,
                'payload_data': payload_data,
                'vulnerability_type': vulnerability_type,
                'target_parameter': target_parameter,
                'url': url
            }

            # البيانات الحقيقية معرفة مسبقاً في بداية الدالة

            logger.info(f"🔥 تمرير البيانات الحقيقية من النظام v4: {v4_data_received}")

            # استدعاء الدالة مع البيانات الحقيقية
            sequence_result = asyncio.run(screenshot_service.capture_vulnerability_sequence(
                url=url,
                vulnerability_name=extracted_vuln_name,
                report_id=report_id,
                payload_data=payload_data,
                vulnerability_type=vulnerability_type,
                v4_data=v4_data_received,
                v4_real_data={
                    'test_results': test_results_received,
                    'exploitation_status': exploitation_status_received,
                    'verification_proof': verification_proof_received,
                    'response_data': response_data_received,
                    'error_messages': error_messages_received,
                    'success_indicators': success_indicators_received,
                    'vulnerability_meta': vulnerability_meta_received,
                    # 🔥 البيانات الشاملة من الدوال الـ36
                    'real_exploitation_evidence': real_exploitation_evidence,
                    'actual_response_content': actual_response_content,
                    'exploitation_results': exploitation_results,
                    'vulnerability_impact_data': vulnerability_impact_data
                }
            ))

            if sequence_result and sequence_result.get('success'):
                logger.info(f"✅ نجح التقاط الصور الثلاث: {sequence_result}")
                all_results = sequence_result.get('stages_results', {})
            else:
                logger.error(f"❌ فشل في التقاط الصور الثلاث: {sequence_result}")
                all_results = {
                    "before": {"success": False, "error": "فشل في capture_vulnerability_sequence"},
                    "during": {"success": False, "error": "فشل في capture_vulnerability_sequence"},
                    "after": {"success": False, "error": "فشل في capture_vulnerability_sequence"}
                }

            # إرجاع نتيجة مجمعة مع البيانات المُحسنة من الدالة الديناميكية
            successful_stages = [stage for stage, result in all_results.items() if result.get('success')]
            total_stages = 3  # before, during, after

            # 🔥 استخراج البيانات المُحسنة من النتائج
            enhanced_data = {}
            for stage_name, stage_result in all_results.items():
                if stage_result and stage_result.get('success'):
                    # استخراج البيانات المُحسنة من أول نتيجة ناجحة
                    if not enhanced_data and stage_result:
                        enhanced_data = {
                            'payload_used': stage_result.get('payload_used'),
                            'url_with_payload': stage_result.get('url_with_payload'),
                            'exploitation_details': stage_result.get('exploitation_details'),
                            'target_parameter': stage_result.get('target_parameter')
                    }
                    break

            result = {
                "success": len(successful_stages) > 0,
                "stages_completed": successful_stages,
                "total_stages": total_stages,
                "results": all_results,
                "message": f"تم التقاط {len(successful_stages)}/{total_stages} صور للثغرة {extracted_vuln_name}",
                "timestamp": datetime.now().isoformat(),
                # 🔥 إضافة البيانات الأصلية
                "vulnerability_name": extracted_vuln_name,
                "vulnerability_type": vulnerability_type,
                "stage": stage,  # المرحلة الأصلية المطلوبة
                "url": url,
                "filename": filename,
                "report_id": report_id,
                # 🔥 إضافة البيانات المُحسنة من الدالة الديناميكية
                "payload_used": enhanced_data.get('payload_used') or payload_data,
                "target_parameter": enhanced_data.get('target_parameter') or target_parameter,
                "url_with_payload": enhanced_data.get('url_with_payload') or url,
                "exploitation_details": enhanced_data.get('exploitation_details') or {
                    'payload': payload_data,
                    'parameter': target_parameter,
                    'vulnerability_type': vulnerability_type,
                    'stage': stage,
                    'timestamp': datetime.now().isoformat()
                }
            }

            logger.info(f"🎉 انتهى التقاط الصور الثلاث للثغرة {extracted_vuln_name}: {len(successful_stages)}/{total_stages} نجح")

        else:
            import concurrent.futures

            try:
                with concurrent.futures.ThreadPoolExecutor() as executor:
                    future = executor.submit(asyncio.run, screenshot_service.capture_single_screenshot(url, filename, report_id))
                    result = future.result(timeout=30)
            except Exception as async_error:
                logger.error(f"❌ خطأ في التقاط الصورة العادية: {async_error}")
                result = {
                    "success": False,
                    "error": f"خطأ في التقاط الصورة: {str(async_error)}",
                    "timestamp": datetime.now().isoformat()
                }

        logger.info(f"🔓 انتهت المعالجة المتسلسلة: {page_key}")

        # إرسال النتيجة
        response_callback(result)

    except Exception as e:
        logger.error(f"❌ خطأ في معالجة الطلب: {e}")
        response_callback({
            "success": False,
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        })
def get_page_key(url):
    """تحويل URL إلى مفتاح صفحة"""
    return url.replace('https://', '').replace('http://', '').replace('/', '_').replace('?', '_').replace('&', '_').replace('=', '_')

# تم حذف تعريف الكلاس المكرر - نستخدم المستورد من screenshot_service.py

# إنشاء خدمة التقاط الصور
screenshot_service = ScreenshotService()

@app.route('/health', methods=['GET'])
def health_check():
    """فحص صحة الخدمة"""
    return jsonify({
        "status": "healthy",
        "service": "Python Screenshot Service v4.0",
        "timestamp": datetime.now().isoformat(),
        "stats": screenshot_service.stats if hasattr(screenshot_service, 'stats') else {}
    })

@app.route('/v4_website', methods=['POST'])
def v4_website_screenshot():
    """التقاط صورة للموقع للنظام v4 مع معالجة متسلسلة صفحة بصفحة"""
    try:
        print("DEBUG: دخل endpoint /v4_website")
        data = request.get_json()
        print(f"DEBUG: البيانات المستلمة: {data}")

        if not data:
            return jsonify({
                'success': False,
                'error': 'لا توجد بيانات في الطلب'
            }), 400

        url = data.get('url')
        filename = data.get('filename', f'v4_screenshot_{int(time.time())}')
        report_id = data.get('report_id', f'report_{int(time.time())}')

        # 🔥 تعريف المتغيرات المطلوبة أولاً
        vulnerability_name = data.get('vulnerability_name', 'Unknown_Vulnerability')
        vulnerability_type = data.get('vulnerability_type', 'Unknown_Type')
        payload_data = data.get('payload_data', '')
        target_parameter = data.get('target_parameter', '')

        # 🔥 تنفيذ اختبار حقيقي في Python للحصول على البيانات الفعلية
        logger.info(f"🔥 بدء الاختبار الحقيقي في Python: {vulnerability_name}")
        real_testing_results = perform_real_vulnerability_test(url, vulnerability_name, vulnerability_type, payload_data, target_parameter)

        # 🔥 تعريف المتغيرات الحقيقية من الاختبار الفعلي
        v4_data_main = data.get('v4_data', {})
        test_results_main = v4_data_main.get('test_results', {})
        exploitation_data_main = v4_data_main.get('exploitation_data', {})
        verification_data_main = v4_data_main.get('verification_data', {})
        vulnerability_meta_main = v4_data_main.get('vulnerability_meta', {})

        # استخدام نتائج الاختبار الحقيقي أولاً، ثم البيانات من v4_data
        # 🔥 إصلاح: test_results_main هو list وليس dict
        real_test_results = real_testing_results.get('evidence', []) + (test_results_main if isinstance(test_results_main, list) else [])
        real_exploitation_status = real_testing_results.get('exploitation_result', 'unknown')
        real_verification_proof = real_testing_results.get('exploitation_steps', []) + (verification_data_main.get('proof', []) if verification_data_main else [])
        real_response_data = real_testing_results.get('response_snippet', '') or v4_data_main.get('response_data', '') or v4_data_main.get('actual_response_content', '')
        real_error_messages = real_testing_results.get('errors', []) + v4_data_main.get('error_messages', [])
        real_success_indicators = real_testing_results.get('success_indicators', []) + v4_data_main.get('success_indicators', [])
        vulnerability_meta = vulnerability_meta_main

        # 🔥 استقبال البيانات الشاملة التفصيلية من الدوال الـ36 (نقل هنا)
        real_exploitation_evidence = v4_data_main.get('real_exploitation_evidence', [])
        actual_response_content = v4_data_main.get('actual_response_content', '')
        exploitation_results = v4_data_main.get('exploitation_results', [])
        vulnerability_impact_data = v4_data_main.get('vulnerability_impact_data', '')

        logger.info(f"📊 البيانات الشاملة المستلمة:")
        logger.info(f"   - real_exploitation_evidence: {len(real_exploitation_evidence)} دليل")
        logger.info(f"   - actual_response_content: {len(actual_response_content)} حرف")
        logger.info(f"   - exploitation_results: {len(exploitation_results)} نتيجة")
        logger.info(f"   - vulnerability_impact_data: {len(vulnerability_impact_data)} حرف")
        comprehensive_details = v4_data_main.get('comprehensive_details', {})
        dynamic_impact = v4_data_main.get('dynamic_impact', {})
        exploitation_steps_comprehensive = v4_data_main.get('exploitation_steps', {})
        dynamic_recommendations = v4_data_main.get('dynamic_recommendations', {})
        comprehensive_analysis = v4_data_main.get('comprehensive_analysis', {})

        logger.info(f"📊 البيانات الشاملة المستلمة:")
        logger.info(f"   - comprehensive_details: {len(str(comprehensive_details))} حرف")
        logger.info(f"   - dynamic_impact: {len(str(dynamic_impact))} حرف")
        logger.info(f"   - exploitation_steps: {len(str(exploitation_steps_comprehensive))} حرف")
        logger.info(f"   - dynamic_recommendations: {len(str(dynamic_recommendations))} حرف")
        logger.info(f"   - comprehensive_analysis: {len(str(comprehensive_analysis))} حرف")

        # 🔥 إضافة معلومات الثغرة الديناميكية
        vulnerability_name = data.get('vulnerability_name')
        vulnerability_type = data.get('vulnerability_type')
        stage = data.get('stage', 'screenshot')
        payload_data = data.get('payload_data')
        target_parameter = data.get('target_parameter')

        # البيانات تم تعريفها مسبقاً في الأعلى

        # 🔥 النظام الديناميكي الكامل - يستخدم البيانات الحقيقية من النظام v4
        # إذا لم تكن هناك بيانات، استخدم قيم افتراضية بسيطة (للتأثيرات فقط، ليس للأسماء)
        original_payload = payload_data
        original_parameter = target_parameter

        if not payload_data or payload_data == 'null' or payload_data is None:
            # إنشاء payload ديناميكي باستخدام الدوال الموجودة
            vuln_name = vulnerability_name if vulnerability_name else "Unknown_Vulnerability"
            if vuln_name and ('XSS' in vuln_name.upper() or 'SCRIPT' in vuln_name.upper()):
                payload_data = f"<script>alert('🔥 {vuln_name} EXPLOITED! 🔥'); document.body.style.background='red';</script>"
            elif vuln_name and 'SQL' in vuln_name.upper():
                payload_data = "1' UNION SELECT 1,2,3,4,5,6,7,8,9,10--"
            elif vuln_name and ('COMMAND' in vuln_name.upper() or 'CMD' in vuln_name.upper()):
                payload_data = "; whoami; id; uname -a"
            else:
                payload_data = f"<script>alert('🔥 {vuln_name} VULNERABILITY! 🔥');</script>"
            logger.info(f"🎯 استخدام payload ديناميكي للثغرة {vuln_name}: {payload_data}")

        if not target_parameter or target_parameter == 'null' or target_parameter is None:
            # إنشاء parameter ديناميكي
            target_parameter = "test_param"
            logger.info(f"🎯 استخدام parameter افتراضي للثغرة {vulnerability_name}: {target_parameter}")

        # استخدام الأسماء الأصلية للملفات والمجلدات (ليس الديناميكية)
        clean_vuln_name = vulnerability_name or 'Unknown'
        clean_filename = data.get('filename', f'{stage}_{clean_vuln_name}_{int(time.time())}')
        logger.info(f"📁 اسم الملف النظيف: {clean_filename}")

        if not url:
            return jsonify({
                'success': False,
                'error': 'لا يوجد URL'
            }), 400

        logger.info(f"🎯 التقاط صورة للنظام v4: {url} -> {filename}")

        # طباعة معلومات الثغرة للتشخيص
        if vulnerability_name:
            logger.info(f"🔍 معلومات الثغرة: {vulnerability_name} ({vulnerability_type}) - المرحلة: {stage}")
            if payload_data:
                logger.info(f"💉 Payload: {payload_data}")
            if target_parameter:
                logger.info(f"🎯 المعامل المستهدف: {target_parameter}")

        # 🔥 نظام queue جديد - رفض الطلبات المتعددة وإضافة للqueue
        page_key = get_page_key(url)

        # بدء thread المعالجة إذا لم يكن موجوداً
        start_processing_thread()

        # إنشاء container للنتيجة
        result_container = {'result': None, 'completed': False}

        def response_callback(result):
            result_container['result'] = result
            result_container['completed'] = True

        # إعداد بيانات الطلب مع البيانات الحقيقية من النظام v4
        request_data = {
            'url': url,
            'filename': filename,
            'report_id': report_id,
            'vulnerability_name': vulnerability_name,
            'vulnerability_type': vulnerability_type,
            'stage': stage,
            'payload_data': payload_data,
            # 🔥 إضافة البيانات الحقيقية من النظام v4
            'v4_real_data': {
                'test_results': real_test_results,
                'exploitation_status': real_exploitation_status,
                'verification_proof': real_verification_proof,
                'response_data': real_response_data,
                'error_messages': real_error_messages,
                'success_indicators': real_success_indicators,
                'vulnerability_meta': vulnerability_meta,
                'original_v4_data': v4_data_main
            },
            'target_parameter': target_parameter,
            'response_callback': response_callback
        }

        # إضافة إلى queue
        request_queue.put(request_data)
        logger.info(f"📥 تم إضافة طلب إلى queue: {page_key} - {stage} - {vulnerability_name}")

        # انتظار النتيجة
        timeout = 60  # 60 ثانية timeout
        start_time = time.time()

        while not result_container['completed']:
            if time.time() - start_time > timeout:
                logger.error(f"❌ timeout في انتظار النتيجة للطلب: {filename}")
                return jsonify({
                    'success': False,
                    'error': 'Timeout waiting for result'
                }), 408

            time.sleep(0.1)  # انتظار قصير

        result = result_container['result']

        # إرجاع النتيجة مع البيانات الأصلية
        if result and result.get('success'):
            logger.info(f"✅ تم التقاط صورة v4 بنجاح: {filename}")

            # 🔥 إضافة البيانات الأصلية للاستجابة
            enhanced_result = {
                **result,  # النتيجة الأصلية
                'vulnerability_name': vulnerability_name,
                'vulnerability_type': vulnerability_type,
                'stage': stage,
                'payload_data': payload_data,
                'target_parameter': target_parameter,
                'url': url,
                'filename': filename,
                'report_id': report_id,
                'timestamp': datetime.now().isoformat()
            }

            return jsonify(enhanced_result)
        else:
            error_msg = result.get('error', 'فشل غير محدد') if result else 'لا توجد نتيجة'
            logger.error(f"❌ فشل في التقاط صورة v4: {error_msg}")
            return jsonify({
                'success': False,
                'error': error_msg,
                'vulnerability_name': vulnerability_name,
                'stage': stage,
                'url': url,
                'filename': filename
            }), 500



    except Exception as e:
        logger.error(f"❌ خطأ في التقاط صورة v4: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

if __name__ == '__main__':
    logger.info("🚀 بدء تشغيل سيرفر Python Screenshot Service")
    logger.info("📡 السيرفر متاح على: http://localhost:8000")
    logger.info("🔗 نقطة النهاية الرئيسية: POST /v4_website")
    logger.info("⚠️ ملاحظة: النظام الرئيسي يعمل على port 3000")

@app.route('/capture', methods=['POST'])
def capture_screenshot():
    """التقاط صورة للموقع مع تتبع الصفحات"""
    try:
        data = request.get_json()

        if not data or 'url' not in data:
            return jsonify({
                "success": False,
                "error": "URL مطلوب"
            }), 400

        url = data['url']
        report_id = data.get('report_id', f'report_{int(datetime.now().timestamp())}')
        width = data.get('width', 1920)
        height = data.get('height', 1080)
        wait_time = data.get('wait_time', 3)

        # 🔥 تتبع الصفحات - التحقق من الصفحة الحالية
        page_id = get_page_identifier(url)
        is_new_page = is_new_page_request(url)

        print(f"📍 طلب التقاط صورة للصفحة: {page_id} ({'جديدة' if is_new_page else 'مستمرة'})")
        print(f"🔗 URL: {url}")
        
        # استخدام النظام الديناميكي الحقيقي للثغرات - الدالة التي تلتقط الصور الثلاث
        vulnerability_name = data.get('vulnerability_name', 'Test_Vulnerability')
        vulnerability_type = data.get('vulnerability_type', 'XSS')

        try:
            # 🔥 استخدام الدالة الأصلية للصور الثلاث مع التأثيرات الحقيقية
            stage = data.get('stage', 'after')
            payload_data = data.get('payload', '')

            if stage == 'sequence':
                # التقاط الصور الثلاث معاً باستخدام الدالة الأصلية
                logger.info(f"🔥 بدء التقاط الصور الثلاث للثغرة: {vulnerability_name}")

                # استدعاء الدالة الأصلية مباشرة
                sequence_data = {
                    'url': url,
                    'vulnerability_name': vulnerability_name,
                    'vulnerability_type': vulnerability_type,
                    'report_id': report_id,
                    'payload_data': payload_data
                }

                # استدعاء الدالة الأصلية للصور الثلاث مباشرة
                logger.info(f"🔥 تحويل الطلب للدالة الأصلية /vulnerability_sequence")

                # إعادة توجيه الطلب للدالة الأصلية
                from flask import request as flask_request
                original_json = flask_request.get_json()
                original_json.update(sequence_data)

                # استدعاء الدالة الأصلية مباشرة
                return capture_vulnerability_sequence_original(original_json)
            else:
                # التقاط صورة واحدة باستخدام الدالة الديناميكية
                result = screenshot_service.capture_vulnerability_screenshot_dynamic(
                    url=url,
                    report_id=report_id,
                    filename=f"{vulnerability_name}_{stage}_{int(time.time())}",
                    vulnerability_name=vulnerability_name,
                    vulnerability_type=vulnerability_type,
                    stage=stage,
                    payload_data=payload_data
                )

            if result and result.get('success'):
                logger.info(f"✅ نجح التقاط الصورة الديناميكية مع التأثيرات: {result.get('screenshot_path')}")
                # تحويل النتيجة لتكون متوافقة مع JSON
                json_result = {
                    'success': True,
                    'message': 'تم التقاط الصورة مع التأثيرات الديناميكية بنجاح',
                    'screenshot_path': str(result.get('screenshot_path', '')),
                    'vulnerability_name': vulnerability_name,
                    'vulnerability_type': vulnerability_type,
                    'stage': stage,
                    'report_id': report_id,
                    'file_size': result.get('file_size', 0)
                }
                result = json_result
            else:
                logger.error(f"❌ فشل التقاط الصورة الديناميكية")
                result = {
                    'success': False,
                    'error': result.get('error', 'فشل في التقاط الصورة الديناميكية') if result else 'فشل غير محدد'
                }

        except Exception as e:
            logger.error(f"❌ خطأ في النظام الديناميكي: {e}")
            result = {
                'success': False,
                'error': str(e)
            }
        
        if result['success']:
            return jsonify(result)
        else:
            return jsonify(result), 500
            
    except Exception as e:
        logger.error(f"❌ خطأ في معالجة الطلب: {str(e)}")
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

def capture_vulnerability_sequence_original(data):
    """دالة مساعدة مبسطة للصور الثلاث"""
    try:
        url = data['url']
        vulnerability_name = data.get('vulnerability_name', 'Test_Vuln')
        vulnerability_type = data.get('vulnerability_type', 'XSS')
        report_id = data.get('report_id', f'report_{int(time.time())}')
        payload_data = data.get('payload_data', 'test_payload')

        logger.info(f"🎯 بدء الصور الثلاث للثغرة: {vulnerability_name}")

        all_results = {}
        stages = ['before', 'during', 'after']

        for i, stage in enumerate(stages):
            logger.info(f"📸 [{i+1}/3] مرحلة {stage}")

            try:
                if i > 0:
                    time.sleep(2)

                result = screenshot_service.capture_vulnerability_screenshot_dynamic(
                    url=url,
                    filename=f"{stage}_{vulnerability_name}",
                    report_id=report_id,
                    vulnerability_name=vulnerability_name,
                    vulnerability_type=vulnerability_type,
                    stage=stage,
                    payload_data=payload_data
                )

                all_results[stage] = result

                if result and result.get('success'):
                    logger.info(f"✅ نجح {stage}")
                else:
                    logger.error(f"❌ فشل {stage}")

            except Exception as e:
                logger.error(f"❌ خطأ في {stage}: {e}")
                all_results[stage] = {"success": False, "error": str(e)}

        successful = sum(1 for r in all_results.values() if r and r.get('success'))

        return jsonify({
            "success": successful > 0,
            "message": f"تم التقاط {successful}/3 صور",
            "stages_results": all_results,
            "successful_stages": successful,
            "total_stages": 3
        })

    except Exception as e:
        logger.error(f"❌ خطأ عام: {e}")
        return jsonify({"success": False, "error": str(e)}), 500

@app.route('/vulnerability_sequence', methods=['POST'])
def capture_vulnerability_sequence():
    """التقاط تسلسل صور للثغرة (قبل/أثناء/بعد) مع تتبع الصفحات"""
    try:
        data = request.get_json()

        if not data or 'url' not in data:
            return jsonify({
                "success": False,
                "error": "URL مطلوب"
            }), 400

        url = data['url']
        vulnerability_name = data.get('vulnerability_name', 'Unknown_Vulnerability')
        vulnerability_type = data.get('vulnerability_type', 'Unknown')
        report_id = data.get('report_id', f'report_{int(datetime.now().timestamp())}')
        payload_data = data.get('payload_data')
        target_parameter = data.get('target_parameter')

        # 🔥 تتبع الصفحات - التحقق من الصفحة الحالية
        page_id = get_page_identifier(url)
        is_new_page = is_new_page_request(url)

        logger.info(f"🎯 بدء تسلسل صور للثغرة: {vulnerability_name}")
        logger.info(f"📍 الصفحة: {page_id} ({'جديدة' if is_new_page else 'مستمرة'})")
        logger.info(f"🔗 URL: {url}")

        # 🔥 إضافة الثغرة لقائمة ثغرات الصفحة الحالية
        current_page_vulnerabilities.append({
            'name': vulnerability_name,
            'type': vulnerability_type,
            'page': page_id,
            'timestamp': datetime.now()
        })

        # 🔥 التقاط الصور الثلاث متسلسلة بالتسلسل الصحيح
        all_results = {}
        stages = ['before', 'during', 'after']

        logger.info(f"🔥 بدء التقاط الصور الثلاث بالتسلسل للثغرة: {vulnerability_name}")

        for i, stage_name in enumerate(stages):
            logger.info(f"📸 [{i+1}/3] التقاط صورة {stage_name.upper()} للثغرة {vulnerability_name}")

            # إنشاء اسم ملف مختلف لكل مرحلة
            stage_filename = f"{stage_name}_{vulnerability_name.replace(' ', '_')}"

            try:
                # 🔥 تأخير بين الصور لضمان التسلسل
                if i > 0:
                    logger.info(f"⏳ تأخير 2 ثانية قبل التقاط صورة {stage_name}")
                    time.sleep(2)

                stage_result = screenshot_service.capture_vulnerability_screenshot_dynamic(
                    url=url,
                    filename=stage_filename,
                    report_id=report_id,
                    vulnerability_name=vulnerability_name,
                    vulnerability_type=vulnerability_type,
                    stage=stage_name,
                    payload_data=payload_data,
                    target_parameter=target_parameter
                )

                all_results[stage_name] = stage_result

                if stage_result and stage_result.get('success'):
                    logger.info(f"✅ [{i+1}/3] نجح التقاط صورة {stage_name.upper()}: {stage_filename}")
                else:
                    logger.error(f"❌ [{i+1}/3] فشل التقاط صورة {stage_name.upper()}: {stage_result.get('error', 'خطأ غير محدد')}")

                # 🔥 انتظار أطول بين المراحل لضمان التسلسل
                if i < len(stages) - 1:  # لا ننتظر بعد الصورة الأخيرة
                    logger.info(f"⏳ انتظار 3 ثواني قبل الانتقال للمرحلة التالية...")
                    time.sleep(3.0)

            except Exception as stage_error:
                logger.error(f"❌ خطأ في التقاط صورة {stage_name}: {stage_error}")
                all_results[stage_name] = {
                    "success": False,
                    "error": str(stage_error),
                    "timestamp": datetime.now().isoformat()
                }

        # 🔥 إرجاع نتيجة مجمعة مع تفاصيل الصفحة
        successful_stages = [stage for stage, result in all_results.items() if result.get('success')]

        logger.info(f"🎉 انتهى التقاط الصور الثلاث للثغرة: {vulnerability_name}")
        logger.info(f"📊 النتيجة: {len(successful_stages)}/{len(stages)} صور نجحت")
        logger.info(f"📍 الصفحة: {page_id}")

        result = {
            "success": len(successful_stages) > 0,
            "stages_completed": successful_stages,
            "total_stages": len(stages),
            "results": all_results,
            "message": f"تم التقاط {len(successful_stages)}/{len(stages)} صور للثغرة {vulnerability_name} في الصفحة {page_id}",
            "page_id": page_id,
            "vulnerability_name": vulnerability_name,
            "timestamp": datetime.now().isoformat()
        }

        logger.info(f"🎉 انتهى تسلسل صور الثغرة {vulnerability_name}: {len(successful_stages)}/{len(stages)} نجح")

        return jsonify(result)

    except Exception as e:
        logger.error(f"❌ خطأ في تسلسل صور الثغرة: {str(e)}")
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500


@app.route('/before_after', methods=['POST'])
def capture_before_after():
    """التقاط صور قبل وبعد للثغرة"""
    try:
        data = request.get_json()

        if not data or 'url' not in data or 'report_id' not in data:
            return jsonify({
                "success": False,
                "error": "URL ومعرف التقرير مطلوبان"
            }), 400

        url = data['url']
        report_id = data['report_id']
        vulnerability_name = data.get('vulnerability_name')

        # استخدام screenshot_service لالتقاط صور قبل وبعد
        import asyncio
        from screenshot_service import capture_before_after_screenshots_v4

        # تشغيل الدالة async
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        result = loop.run_until_complete(capture_before_after_screenshots_v4(url, report_id, vulnerability_name))
        loop.close()

        if result and result.get('success'):
            return jsonify({
                "success": True,
                "before": result.get('before'),
                "after": result.get('after'),
                "report_id": report_id
            })
        else:
            return jsonify({
                "success": False,
                "error": "فشل في التقاط صور قبل وبعد"
            }), 500

    except Exception as e:
        logger.error(f"❌ خطأ في التقاط صور قبل وبعد: {str(e)}")
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

@app.route('/stats', methods=['GET'])
def get_stats():
    """الحصول على إحصائيات الخدمة"""
    return jsonify(screenshot_service.stats)

@app.route('/save_screenshot', methods=['POST'])
def save_screenshot():
    """حفظ الصورة في مجلد منفصل"""
    try:
        data = request.get_json()

        if not data:
            return jsonify({"error": "لا توجد بيانات"}), 400

        screenshot_data = data.get('screenshot_data')
        filename = data.get('filename')
        report_id = data.get('report_id')

        if not all([screenshot_data, filename, report_id]):
            return jsonify({"error": "بيانات مفقودة: screenshot_data, filename, report_id"}), 400

        # إنشاء مجلد التقرير
        report_dir = Path("screenshots") / report_id
        report_dir.mkdir(parents=True, exist_ok=True)

        # تحديد مسار الملف
        file_path = report_dir / f"{filename}.png"

        # تحويل Base64 إلى صورة
        try:
            # إزالة البادئة إذا كانت موجودة
            if screenshot_data.startswith('data:image'):
                screenshot_data = screenshot_data.split(',')[1]

            # فك تشفير Base64
            image_data = base64.b64decode(screenshot_data)

            # حفظ الصورة
            with open(file_path, 'wb') as f:
                f.write(image_data)

            logger.info(f"✅ تم حفظ الصورة: {file_path}")

            return jsonify({
                "success": True,
                "file_path": str(file_path),
                "message": f"تم حفظ الصورة بنجاح في {file_path}"
            })

        except Exception as decode_error:
            logger.error(f"❌ خطأ في فك تشفير الصورة: {decode_error}")
            return jsonify({"error": f"خطأ في فك تشفير الصورة: {str(decode_error)}"}), 400

    except Exception as e:
        logger.error(f"❌ خطأ في حفظ الصورة: {e}")
        return jsonify({"error": f"خطأ في حفظ الصورة: {str(e)}"}), 500



@app.route('/', methods=['GET'])
def index():
    """الصفحة الرئيسية"""
    return jsonify({
        "service": "Python Screenshot Service v4.0",
        "status": "running",
        "endpoints": {
            "/health": "فحص صحة الخدمة",
            "/capture": "التقاط صورة (POST)",
            "/save_screenshot": "حفظ الصورة في مجلد (POST)",
            "/v4_website": "التقاط صورة للنظام v4 (POST)",
            "/vulnerability_sequence": "التقاط تسلسل صور للثغرة (POST)",
            "/get_report_screenshots": "قراءة صور التقرير المحفوظة (POST)",
            "/stats": "إحصائيات الخدمة"
        }
    })



@app.route('/get_report_screenshots', methods=['POST'])
def get_report_screenshots():
    """قراءة جميع الصور المحفوظة لتقرير معين"""
    try:
        data = request.get_json()

        if not data:
            return jsonify({
                'success': False,
                'error': 'لا توجد بيانات في الطلب'
            }), 400

        report_id = data.get('report_id')

        if not report_id:
            return jsonify({
                'success': False,
                'error': 'معرف التقرير مطلوب'
            }), 400

        logger.info(f"📂 قراءة الصور المحفوظة للتقرير: {report_id}")

        # مسار مجلد التقرير
        report_dir = Path("screenshots") / report_id

        if not report_dir.exists():
            return jsonify({
                'success': True,
                'screenshots': [],
                'message': f'مجلد التقرير غير موجود: {report_id}'
            })

        screenshots = []

        # قراءة جميع ملفات PNG في المجلد
        for img_file in report_dir.glob("*.png"):
            try:
                # قراءة الصورة وتحويلها إلى base64
                with open(img_file, 'rb') as f:
                    img_data = f.read()
                    base64_data = base64.b64encode(img_data).decode('utf-8')

                screenshots.append({
                    'filename': img_file.stem,  # اسم الملف بدون امتداد
                    'full_filename': img_file.name,
                    'screenshot_data': base64_data,
                    'file_path': str(img_file.absolute()),
                    'file_size': len(img_data),
                    'created_time': datetime.fromtimestamp(img_file.stat().st_ctime).isoformat()
                })

                logger.info(f"📸 تم قراءة الصورة: {img_file.name}")

            except Exception as file_error:
                logger.error(f"❌ خطأ في قراءة الصورة {img_file.name}: {file_error}")
                continue

        logger.info(f"✅ تم قراءة {len(screenshots)} صورة للتقرير: {report_id}")

        return jsonify({
            'success': True,
            'screenshots': screenshots,
            'total_screenshots': len(screenshots),
            'report_id': report_id,
            'report_dir': str(report_dir.absolute())
        })

    except Exception as e:
        logger.error(f"❌ خطأ في قراءة صور التقرير: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


if __name__ == '__main__':
    print("🔥" * 50)
    print("🔥 بدء تشغيل خدمة الصور Python")
    print("🔥" * 50)

    # إنشاء مجلد الصور إذا لم يكن موجوداً
    screenshots_dir = Path("screenshots")
    screenshots_dir.mkdir(exist_ok=True)

    print(f"📁 مجلد الصور: {screenshots_dir.absolute()}")
    print(f"🌐 السيرفر يعمل على: http://localhost:8000")
    print(f"🔍 endpoint الالتقاط: http://localhost:8000/capture")
    print("🚀 السيرفر جاهز لاستقبال الطلبات...")

# 🔥 دالة الاختبار الحقيقي للثغرات في Python
def perform_real_vulnerability_test(url, vuln_name, vuln_type, payload, parameter):
    """تنفيذ اختبار حقيقي للثغرة في Python وجمع البيانات الفعلية"""

    logger.info(f"🔥 بدء الاختبار الحقيقي في Python: {vuln_name}")

    test_results = {
        'evidence': [],
        'responses_received': [],
        'exploitation_steps': [],
        'payloads_tested': [],
        'errors': [],
        'success_indicators': [],
        'exploitation_result': 'unknown',
        'response_snippet': '',
        'full_response_content': ''
    }

    try:
        # إنشاء URL للاختبار
        if '?' in url:
            test_url = f"{url}&{parameter}={payload}"
        else:
            test_url = f"{url}?{parameter}={payload}"

        test_results['payloads_tested'].append(f"Testing payload: {payload} on parameter: {parameter}")
        test_results['exploitation_steps'].append(f"Created test URL: {test_url}")

        logger.info(f"📡 إرسال طلب حقيقي إلى: {test_url}")

        # تنفيذ الطلب الحقيقي
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive'
        }

        start_time = time.time()
        response = requests.get(test_url, headers=headers, timeout=10)
        end_time = time.time()

        # جمع البيانات الأساسية
        test_results['evidence'].append(f"Real HTTP Response Code: {response.status_code}")
        test_results['evidence'].append(f"Response Size: {len(response.text)} characters")
        test_results['evidence'].append(f"Response Time: {(end_time - start_time)*1000:.2f}ms")

        test_results['responses_received'].append(f"HTTP {response.status_code}: {response.reason}")
        test_results['responses_received'].append(f"Content-Type: {response.headers.get('content-type', 'unknown')}")
        test_results['responses_received'].append(f"Server: {response.headers.get('server', 'unknown')}")

        # حفظ محتوى الاستجابة
        test_results['full_response_content'] = response.text
        test_results['response_snippet'] = response.text[:2000]  # أول 2000 حرف

        # تحليل الاستجابة حسب نوع الثغرة
        response_lower = response.text.lower()

        if 'sql' in vuln_type.lower():
            # تحليل SQL Injection
            sql_errors = [
                'mysql error', 'sql syntax', 'ora-', 'microsoft odbc', 'postgresql error',
                'sqlite error', 'sql server', 'syntax error', 'mysql_fetch', 'pg_query'
            ]

            for error in sql_errors:
                if error in response_lower:
                    test_results['evidence'].append(f"SQL INJECTION CONFIRMED: Database error detected - {error}")
                    test_results['success_indicators'].append(f"SQL error found: {error}")

                    # استخراج رسالة الخطأ الفعلية
                    error_index = response_lower.find(error)
                    if error_index != -1:
                        error_context = response.text[max(0, error_index-100):error_index+200]
                        test_results['evidence'].append(f"SQL_ERROR_CONTEXT: {error_context}")

            if 'union' in response_lower and 'select' in response_lower:
                test_results['evidence'].append("SQL INJECTION CONFIRMED: UNION SELECT detected in response")
                test_results['success_indicators'].append("UNION SELECT injection successful")

        elif 'xss' in vuln_type.lower():
            # تحليل XSS
            if payload.lower() in response_lower:
                test_results['evidence'].append("XSS CONFIRMED: Payload reflected in response")
                test_results['success_indicators'].append("XSS payload reflection confirmed")

            if '<script>' in response_lower or 'javascript:' in response_lower:
                test_results['evidence'].append("XSS CONFIRMED: JavaScript code detected in response")
                test_results['success_indicators'].append("JavaScript injection confirmed")

        elif 'lfi' in vuln_type.lower() or 'file' in vuln_type.lower():
            # تحليل LFI
            file_indicators = [
                'root:x:', '/bin/bash', '/etc/passwd', 'daemon:x:', 'www-data:',
                '[boot loader]', 'config.php', 'database', 'password'
            ]

            for indicator in file_indicators:
                if indicator in response_lower:
                    test_results['evidence'].append(f"LFI CONFIRMED: System file content detected - {indicator}")
                    test_results['success_indicators'].append(f"File content found: {indicator}")

        # تحديد نجاح الاستغلال
        if len(test_results['success_indicators']) > 0:
            test_results['exploitation_result'] = 'successful'
            test_results['success_indicators'].append('Vulnerability confirmed through real testing')
        else:
            test_results['exploitation_result'] = 'attempted'
            test_results['success_indicators'].append('Vulnerability testing completed')

        test_results['exploitation_steps'].append(f"Response analysis completed: {test_results['exploitation_result']}")
        test_results['exploitation_steps'].append(f"Evidence collected: {len(test_results['evidence'])} items")

        logger.info(f"✅ اكتمل الاختبار الحقيقي: {vuln_name} - النتيجة: {test_results['exploitation_result']}")
        logger.info(f"📊 تم جمع {len(test_results['evidence'])} دليل حقيقي")

    except Exception as error:
        logger.error(f"❌ خطأ في الاختبار الحقيقي: {error}")
        test_results['errors'].append(f"Testing error: {str(error)}")
        test_results['evidence'].append(f"Error during testing: {str(error)}")
        test_results['exploitation_result'] = 'error'

    return test_results

if __name__ == '__main__':
    print("🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥")
    print("🔥 بدء تشغيل خدمة الصور Python")
    print("🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥")

    # إنشاء مجلد الصور إذا لم يكن موجوداً
    screenshots_dir = Path(__file__).parent / "screenshots"
    screenshots_dir.mkdir(exist_ok=True)
    print(f"📁 مجلد الصور: {screenshots_dir}")

    print(f"🌐 السيرفر يعمل على: http://localhost:8000")
    print(f"🔍 endpoint الالتقاط: http://localhost:8000/capture")
    print("🚀 السيرفر جاهز لاستقبال الطلبات...")

    # تشغيل السيرفر بدون debug mode لتجنب إعادة التشغيل أثناء Playwright
    app.run(
        host='0.0.0.0',
        port=8000,
        debug=False,  # 🔥 تعطيل debug mode لضمان استقرار Playwright
        threaded=True
    )