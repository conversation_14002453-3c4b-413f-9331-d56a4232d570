#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json
import time
import os
from pathlib import Path

def create_real_test_with_images():
    """إنشاء اختبار حقيقي مع صور وتقارير"""
    
    print("🔥 إنشاء اختبار حقيقي مع صور وتقارير")
    print("🎯 إنشاء ثغرات جديدة مع صور وتوليد تقارير صحيحة")
    print("=" * 70)
    
    # 1. إنشاء ثغرات جديدة مع صور
    report_id = f"final_test_{int(time.time())}"
    vulnerabilities = create_vulnerabilities_with_correct_images(report_id)
    
    if vulnerabilities:
        print(f"✅ تم إنشاء {len(vulnerabilities)} ثغرة مع صور")
        
        # 2. إنشاء تقارير مع مسارات صحيحة
        create_corrected_reports(vulnerabilities, report_id)
        
        # 3. فحص النتائج
        verify_final_results(report_id)
    else:
        print("❌ فشل في إنشاء الثغرات")

def create_vulnerabilities_with_correct_images(report_id):
    """إنشاء ثغرات مع صور ومسارات صحيحة"""
    
    print(f"\n📸 إنشاء ثغرات جديدة مع صور - Report ID: {report_id}")
    
    vulnerabilities = []
    
    # ثغرات للاختبار
    test_vulns = [
        {
            'name': 'XSS Cross Site Scripting',
            'type': 'XSS',
            'payload': '<script>alert("FINAL_XSS_TEST")</script>',
            'param': 'searchFor',
            'severity': 'High'
        },
        {
            'name': 'SQL Injection Attack', 
            'type': 'SQL_Injection',
            'payload': "' UNION SELECT 'FINAL_SQL_TEST', version() --",
            'param': 'id',
            'severity': 'Critical'
        }
    ]
    
    for i, vuln in enumerate(test_vulns, 1):
        print(f"\n🎯 إنشاء ثغرة {i}: {vuln['name']}")
        
        # إنشاء الصور الثلاث
        vuln_with_images = create_vulnerability_with_absolute_paths(vuln, report_id, i)
        
        if vuln_with_images:
            vulnerabilities.append(vuln_with_images)
            print(f"   ✅ تم إنشاء ثغرة {i} مع مسارات صحيحة")
        else:
            print(f"   ❌ فشل في إنشاء ثغرة {i}")
    
    return vulnerabilities

def create_vulnerability_with_absolute_paths(vuln, report_id, vuln_num):
    """إنشاء ثغرة مع مسارات مطلقة صحيحة"""
    
    stages = ['before', 'during', 'after']
    image_paths = {}
    absolute_paths = {}
    
    for stage in stages:
        print(f"      📸 إنشاء صورة {stage}...")
        
        data = {
            'url': 'http://testphp.vulnweb.com/search.php',
            'filename': f'{stage}_{vuln["type"]}_FINAL_TEST_{vuln_num}',
            'report_id': report_id,
            'vulnerability_name': vuln['name'],
            'vulnerability_type': vuln['type'],
            'stage': stage,
            'payload_data': vuln['payload'],
            'target_parameter': vuln['param']
        }
        
        try:
            response = requests.post(
                'http://localhost:8000/v4_website',
                json=data,
                timeout=45
            )
            
            if response.status_code == 200:
                result = response.json()
                
                if result.get('success'):
                    file_path = result.get('file_path', '')
                    size = result.get('file_size', 0)
                    
                    if file_path and size > 0:
                        # استخدام المسار المطلق
                        absolute_path = file_path
                        image_paths[stage] = absolute_path
                        absolute_paths[f'{stage}_screenshot_path'] = absolute_path
                        
                        print(f"         ✅ {size:,} bytes - {absolute_path}")
                    else:
                        print(f"         ❌ مسار فارغ أو حجم 0")
                else:
                    print(f"         ❌ فشل: {result.get('error', 'Unknown')}")
            else:
                print(f"         ❌ HTTP {response.status_code}")
                
        except Exception as e:
            print(f"         ❌ خطأ: {e}")
    
    # إنشاء بيانات الثغرة الكاملة
    if len(image_paths) >= 2:
        vuln_complete = {
            'name': vuln['name'],
            'type': vuln['type'],
            'severity': vuln['severity'],
            'description': f'Final test vulnerability: {vuln["name"]}',
            'impact': f'High impact {vuln["type"]} vulnerability with visual proof',
            'recommendation': f'Fix {vuln["type"]} vulnerability immediately',
            
            # مسارات الصور المطلقة
            'screenshots': image_paths,
            'visual_proof': absolute_paths,
            
            # معلومات إضافية
            'payload': vuln['payload'],
            'parameter': vuln['param'],
            'report_id': report_id
        }
        
        return vuln_complete
    
    return None

def create_corrected_reports(vulnerabilities, report_id):
    """إنشاء تقارير مع مسارات صحيحة"""
    
    print(f"\n📋 إنشاء تقارير مع مسارات صحيحة...")
    
    # إنشاء التقرير الرئيسي
    main_report_html = generate_corrected_main_report(vulnerabilities, report_id)
    main_report_path = f"corrected_main_report_{report_id}.html"
    
    with open(main_report_path, 'w', encoding='utf-8') as f:
        f.write(main_report_html)
    
    print(f"✅ تم إنشاء التقرير الرئيسي المُصحح: {main_report_path}")
    
    # إنشاء التقرير المنفصل
    separate_report_html = generate_corrected_separate_report(vulnerabilities, report_id)
    separate_report_path = f"corrected_separate_report_{report_id}.html"
    
    with open(separate_report_path, 'w', encoding='utf-8') as f:
        f.write(separate_report_html)
    
    print(f"✅ تم إنشاء التقرير المنفصل المُصحح: {separate_report_path}")
    
    return main_report_path, separate_report_path

def generate_corrected_main_report(vulnerabilities, report_id):
    """إنشاء التقرير الرئيسي مع مسارات صحيحة"""
    
    html = f"""
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>التقرير الرئيسي المُصحح v4</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }}
        .container {{ max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; }}
        .vulnerability {{ margin: 30px 0; padding: 20px; border: 1px solid #ddd; border-radius: 8px; }}
        .section {{ margin: 20px 0; padding: 15px; background: #f8f9fa; border-radius: 5px; }}
        .visual-changes {{ background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%); border: 2px solid #17a2b8; }}
        .image-container {{ margin: 15px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; background: white; }}
        .image-container img {{ max-width: 500px; max-height: 400px; border: 2px solid #007bff; border-radius: 5px; }}
        .success {{ color: #155724; background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0; }}
        .error {{ color: #721c24; background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0; }}
        .path-info {{ font-family: monospace; background: #e9ecef; padding: 8px; border-radius: 3px; font-size: 12px; }}
        h1 {{ color: #007bff; text-align: center; }}
        h2 {{ color: #28a745; border-bottom: 2px solid #28a745; padding-bottom: 10px; }}
        h3 {{ color: #17a2b8; }}
    </style>
</head>
<body>
    <div class="container">
        <h1>🔥 التقرير الرئيسي المُصحح v4</h1>
        <p><strong>تاريخ الإنشاء:</strong> {time.strftime('%Y-%m-%d %H:%M:%S')}</p>
        <p><strong>Report ID:</strong> {report_id}</p>
        <p><strong>عدد الثغرات:</strong> {len(vulnerabilities)}</p>
        
        <div class="section">
            <h2>📊 ملخص الثغرات</h2>
            <p>تم اكتشاف {len(vulnerabilities)} ثغرة أمنية مع صور توضيحية كاملة ومسارات صحيحة.</p>
        </div>
"""

    for i, vuln in enumerate(vulnerabilities, 1):
        html += f"""
        <div class="vulnerability">
            <h2>🎯 الثغرة {i}: {vuln['name']}</h2>
            <p><strong>النوع:</strong> {vuln['type']}</p>
            <p><strong>الخطورة:</strong> {vuln['severity']}</p>
            <p><strong>الوصف:</strong> {vuln['description']}</p>
            <p><strong>التأثير:</strong> {vuln['impact']}</p>
            
            <div class="section visual-changes">
                <h3>📸 الصور التوضيحية والتأثيرات البصرية</h3>
                <p>الصور التالية تُظهر الثغرة في مراحل مختلفة من الاستغلال:</p>
"""
        
        # إضافة الصور مع مسارات صحيحة
        stages = ['before', 'during', 'after']
        stage_names = {'before': 'قبل الاستغلال', 'during': 'أثناء الاستغلال', 'after': 'بعد الاستغلال'}
        
        for stage in stages:
            if stage in vuln['screenshots']:
                image_path = vuln['screenshots'][stage]
                html += f"""
                <div class="image-container">
                    <h4>{stage_names[stage]}:</h4>
                    <div class="path-info">المسار: {image_path}</div>
                    <img src="file:///{image_path.replace('\\', '/')}" alt="{stage_names[stage]}" 
                         onload="this.nextElementSibling.style.display='block'; this.nextElementSibling.nextElementSibling.style.display='none';"
                         onerror="this.style.display='none'; this.nextElementSibling.style.display='none'; this.nextElementSibling.nextElementSibling.style.display='block';">
                    <div class="success" style="display:none;">✅ تم تحميل الصورة بنجاح</div>
                    <div class="error" style="display:none;">❌ فشل في تحميل الصورة: {image_path}</div>
                </div>
"""
        
        html += f"""
            </div>
            
            <div class="section">
                <h3>💡 التوصيات</h3>
                <p>{vuln['recommendation']}</p>
            </div>
        </div>
"""
    
    html += """
    </div>
    
    <script>
        console.log('🔥 تم تحميل التقرير الرئيسي المُصحح v4');
        
        let imageStats = { total: 0, loaded: 0, failed: 0 };
        
        document.querySelectorAll('img').forEach(img => {
            imageStats.total++;
            
            img.addEventListener('load', function() {
                imageStats.loaded++;
                console.log(`✅ تم تحميل صورة (${imageStats.loaded}/${imageStats.total})`);
            });
            
            img.addEventListener('error', function() {
                imageStats.failed++;
                console.log(`❌ فشل في تحميل صورة (${imageStats.failed}/${imageStats.total})`);
            });
        });
        
        setTimeout(() => {
            console.log(`📊 إحصائيات نهائية: ${imageStats.loaded} نجحت، ${imageStats.failed} فشلت من أصل ${imageStats.total}`);
        }, 3000);
    </script>
</body>
</html>
"""
    
    return html

def generate_corrected_separate_report(vulnerabilities, report_id):
    """إنشاء التقرير المنفصل مع مسارات صحيحة"""
    
    html = f"""
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>التقرير المنفصل المُصحح v4</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; background: #f8f9fa; }}
        .container {{ max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; }}
        .vulnerability {{ margin: 30px 0; padding: 25px; border: 2px solid #17a2b8; border-radius: 10px; }}
        .section {{ margin: 20px 0; padding: 20px; background: #e9ecef; border-radius: 8px; }}
        .visual-changes {{ background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%); border: 2px solid #17a2b8; }}
        .image-container {{ margin: 15px 0; padding: 15px; border: 1px solid #ccc; border-radius: 5px; background: white; }}
        .image-container img {{ max-width: 500px; max-height: 400px; border: 2px solid #17a2b8; border-radius: 5px; }}
        .success {{ color: #155724; background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0; }}
        .error {{ color: #721c24; background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0; }}
        .path-info {{ font-family: monospace; background: #f8f9fa; padding: 8px; border-radius: 3px; font-size: 12px; }}
        h1 {{ color: #17a2b8; text-align: center; }}
        h2 {{ color: #495057; }}
        h3 {{ color: #17a2b8; }}
    </style>
</head>
<body>
    <div class="container">
        <h1>📄 التقرير المنفصل المُصحح v4</h1>
        <p><strong>تقرير مفصل يحتوي على جميع الأدلة البصرية للثغرات المكتشفة</strong></p>
        <p><strong>Report ID:</strong> {report_id}</p>
"""

    for i, vuln in enumerate(vulnerabilities, 1):
        html += f"""
        <div class="vulnerability">
            <h2>📋 تفاصيل الثغرة {i}: {vuln['name']}</h2>
            
            <div class="section">
                <h3>ℹ️ معلومات الثغرة</h3>
                <p><strong>النوع:</strong> {vuln['type']}</p>
                <p><strong>مستوى الخطورة:</strong> {vuln['severity']}</p>
                <p><strong>الوصف التفصيلي:</strong> {vuln['description']}</p>
            </div>
            
            <div class="section visual-changes">
                <h3>🖼️ الأدلة البصرية (Visual Proof)</h3>
                <p>الصور التالية تُظهر الدليل البصري على وجود الثغرة وتأثيرها:</p>
"""
        
        # إضافة الصور من visual_proof
        stages = ['before', 'during', 'after']
        stage_names = {'before': 'الحالة الأصلية', 'during': 'عملية الاستغلال', 'after': 'نتيجة الاستغلال'}
        
        for stage in stages:
            key = f'{stage}_screenshot_path'
            if key in vuln['visual_proof']:
                image_path = vuln['visual_proof'][key]
                html += f"""
                <div class="image-container">
                    <h4>{stage_names[stage]}:</h4>
                    <div class="path-info">المسار: {image_path}</div>
                    <img src="file:///{image_path.replace('\\', '/')}" alt="{stage_names[stage]}" 
                         onload="this.nextElementSibling.style.display='block'; this.nextElementSibling.nextElementSibling.style.display='none';"
                         onerror="this.style.display='none'; this.nextElementSibling.style.display='none'; this.nextElementSibling.nextElementSibling.style.display='block';">
                    <div class="success" style="display:none;">✅ تم تحميل الصورة بنجاح</div>
                    <div class="error" style="display:none;">❌ فشل في تحميل الصورة: {image_path}</div>
                </div>
"""
        
        html += f"""
            </div>
            
            <div class="section">
                <h3>⚠️ تحليل التأثير</h3>
                <p>{vuln['impact']}</p>
            </div>
            
            <div class="section">
                <h3>🔧 خطوات الإصلاح</h3>
                <p>{vuln['recommendation']}</p>
            </div>
        </div>
"""
    
    html += """
    </div>
    
    <script>
        console.log('📄 تم تحميل التقرير المنفصل المُصحح v4');
        
        let separateStats = { total: 0, loaded: 0, failed: 0 };
        
        document.querySelectorAll('img').forEach(img => {
            separateStats.total++;
            
            img.addEventListener('load', function() {
                separateStats.loaded++;
                console.log(`✅ تم تحميل صورة في التقرير المنفصل (${separateStats.loaded}/${separateStats.total})`);
            });
            
            img.addEventListener('error', function() {
                separateStats.failed++;
                console.log(`❌ فشل في تحميل صورة في التقرير المنفصل (${separateStats.failed}/${separateStats.total})`);
            });
        });
        
        setTimeout(() => {
            console.log(`📊 إحصائيات التقرير المنفصل: ${separateStats.loaded} نجحت، ${separateStats.failed} فشلت من أصل ${separateStats.total}`);
        }, 3000);
    </script>
</body>
</html>
"""
    
    return html

def verify_final_results(report_id):
    """فحص النتائج النهائية"""
    
    print(f"\n🔍 فحص النتائج النهائية:")
    print("-" * 50)
    
    # فحص الملفات المُنشأة
    main_report = f"corrected_main_report_{report_id}.html"
    separate_report = f"corrected_separate_report_{report_id}.html"
    
    if os.path.exists(main_report):
        size = os.path.getsize(main_report)
        print(f"✅ التقرير الرئيسي: {main_report} ({size:,} bytes)")
    else:
        print(f"❌ التقرير الرئيسي غير موجود")
    
    if os.path.exists(separate_report):
        size = os.path.getsize(separate_report)
        print(f"✅ التقرير المنفصل: {separate_report} ({size:,} bytes)")
    else:
        print(f"❌ التقرير المنفصل غير موجود")
    
    # فحص مجلد الصور
    screenshots_dir = Path(f"screenshots/{report_id}")
    if screenshots_dir.exists():
        images = list(screenshots_dir.glob("*.png"))
        print(f"✅ مجلد الصور: {len(images)} صورة")
        
        for image in images:
            size = image.stat().st_size
            print(f"   📸 {image.name} - {size:,} bytes")
    else:
        print(f"❌ مجلد الصور غير موجود")
    
    print(f"\n🎉 انتهى الاختبار!")
    print(f"📖 افتح التقارير في المتصفح:")
    print(f"   📋 التقرير الرئيسي: file:///{os.path.abspath(main_report)}")
    print(f"   📄 التقرير المنفصل: file:///{os.path.abspath(separate_report)}")

if __name__ == "__main__":
    create_real_test_with_images()
