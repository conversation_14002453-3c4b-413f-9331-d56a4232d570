#!/usr/bin/env python3
"""
🔥 اختبار الدالة الأصلية للصور الثلاث مع التأثيرات الحقيقية
استخدام /vulnerability_sequence للحصول على الصور الثلاث معاً
"""

import json
import time
import requests
from datetime import datetime
import os

# ثغرات للاختبار مع الدالة الأصلية
VULNERABILITIES_FOR_ORIGINAL_TEST = [
    {
        "name": "XSS_Critical_Real_Effects",
        "type": "Cross-Site Scripting",
        "url": "http://testphp.vulnweb.com/artists.php?artist=1",
        "payload": "<script>alert('🔥 XSS EXPLOITED! 🔥'); document.body.style.background='red';</script>",
        "description": "ثغرة XSS مع تأثيرات حقيقية"
    },
    {
        "name": "SQL_Injection_Real_Impact",
        "type": "SQL Injection", 
        "url": "http://testphp.vulnweb.com/listproducts.php?cat=1",
        "payload": "1' UNION SELECT 1,2,3,4,5,6,7,8,9,10,11--",
        "description": "ثغرة SQL Injection مع تأثيرات حقيقية"
    },
    {
        "name": "DOM_XSS_Visual_Impact",
        "type": "DOM-based XSS",
        "url": "https://httpbin.org/html",
        "payload": "<script>document.body.innerHTML='<h1 style=\"color:red;font-size:50px;\">🚨 DOM XSS! 🚨</h1>';</script>",
        "description": "ثغرة DOM XSS مع تأثيرات بصرية"
    }
]

class OriginalThreeStagesTester:
    def __init__(self):
        self.base_url = "http://localhost:8000"
        self.results = []
        
    def log(self, message):
        timestamp = datetime.now().strftime("%H:%M:%S")
        print(f"[{timestamp}] {message}")
        
    def test_server_connection(self):
        """اختبار الاتصال بالسيرفر"""
        try:
            # اختبار endpoint بسيط
            test_payload = {
                "url": "https://httpbin.org/html",
                "vulnerability_name": "Connection_Test"
            }
            response = requests.post(f"{self.base_url}/capture", json=test_payload, timeout=10)
            if response.status_code in [200, 500]:  # أي رد يعني أن السيرفر يعمل
                self.log("✅ السيرفر يعمل بشكل صحيح")
                return True
            else:
                self.log(f"❌ السيرفر يرد بكود: {response.status_code}")
                return False
        except requests.exceptions.ConnectionError:
            self.log("❌ لا يمكن الاتصال بالسيرفر")
            return False
        except Exception as e:
            self.log(f"❌ خطأ في الاتصال: {e}")
            return False
            
    def test_vulnerability_with_original_function(self, vuln_data):
        """اختبار الثغرة باستخدام الدالة الأصلية للصور الثلاث"""
        self.log(f"\n🔥 اختبار الثغرة باستخدام الدالة الأصلية: {vuln_data['name']}")
        self.log(f"📋 النوع: {vuln_data['type']}")
        self.log(f"🔗 URL: {vuln_data['url']}")
        
        try:
            # إعداد البيانات للدالة الأصلية
            payload = {
                "url": vuln_data["url"],
                "vulnerability_name": vuln_data["name"],
                "vulnerability_type": vuln_data["type"],
                "report_id": f"{vuln_data['name']}_{int(time.time())}",
                "payload_data": vuln_data["payload"],
                "target_parameter": "test_param"
            }
            
            self.log(f"📤 إرسال طلب للدالة الأصلية /vulnerability_sequence...")
            self.log(f"📋 البيانات: {json.dumps(payload, indent=2, ensure_ascii=False)}")
            
            # استدعاء الدالة الأصلية للصور الثلاث
            response = requests.post(
                f"{self.base_url}/vulnerability_sequence",
                json=payload,
                timeout=180  # مهلة معقولة للصور الثلاث
            )
            
            self.log(f"📥 رد السيرفر: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    result = response.json()
                    
                    self.log(f"✅ نجح الطلب!")
                    self.log(f"📊 النتيجة: {result.get('message', 'غير محدد')}")
                    
                    # تحليل نتائج المراحل الثلاث
                    stages_results = result.get('stages_results', {})
                    successful_stages = result.get('successful_stages', 0)
                    total_stages = result.get('total_stages', 3)
                    
                    self.log(f"📈 المراحل الناجحة: {successful_stages}/{total_stages}")
                    
                    # تفاصيل كل مرحلة
                    for stage, stage_result in stages_results.items():
                        if stage_result and stage_result.get('success'):
                            screenshot_path = stage_result.get('screenshot_path', 'غير محدد')
                            file_size = stage_result.get('file_size', 0)
                            self.log(f"   📸 {stage.upper()}: ✅ ({file_size} bytes) - {screenshot_path}")
                            
                            # التحقق من وجود الملف
                            if os.path.exists(screenshot_path):
                                self.log(f"      📁 الملف موجود: ✅")
                            else:
                                self.log(f"      ⚠️ الملف غير موجود")
                        else:
                            error = stage_result.get('error', 'خطأ غير محدد') if stage_result else 'لا توجد نتيجة'
                            self.log(f"   📸 {stage.upper()}: ❌ ({error})")
                    
                    return {
                        'success': True,
                        'vulnerability': vuln_data,
                        'stages_results': stages_results,
                        'successful_stages': successful_stages,
                        'total_stages': total_stages,
                        'response': result
                    }
                    
                except json.JSONDecodeError:
                    self.log(f"❌ خطأ في تحليل JSON: {response.text[:300]}")
                    return {'success': False, 'error': 'JSON decode error'}
            else:
                self.log(f"❌ فشل الطلب: {response.status_code}")
                self.log(f"📄 Response: {response.text[:500]}")
                return {'success': False, 'error': f'HTTP {response.status_code}'}
                
        except requests.exceptions.Timeout:
            self.log(f"⏰ انتهت مهلة الطلب للثغرة {vuln_data['name']}")
            return {'success': False, 'error': 'Timeout'}
        except Exception as e:
            self.log(f"❌ خطأ في الاختبار: {e}")
            return {'success': False, 'error': str(e)}
        
    def run_original_function_test(self):
        """تشغيل اختبار الدالة الأصلية"""
        self.log("🚀 بدء اختبار الدالة الأصلية للصور الثلاث")
        
        # اختبار الاتصال
        if not self.test_server_connection():
            self.log("❌ فشل الاختبار - السيرفر لا يعمل")
            return False
            
        self.log("✅ السيرفر متصل - بدء اختبار الدالة الأصلية...")
        
        # اختبار الثغرات باستخدام الدالة الأصلية
        for i, vuln in enumerate(VULNERABILITIES_FOR_ORIGINAL_TEST):
            self.log(f"\n{'='*80}")
            self.log(f"🎯 اختبار الثغرة {i+1}/{len(VULNERABILITIES_FOR_ORIGINAL_TEST)}: {vuln['name']}")
            self.log(f"{'='*80}")
            
            result = self.test_vulnerability_with_original_function(vuln)
            self.results.append(result)
            
            # انتظار بين الثغرات
            if i < len(VULNERABILITIES_FOR_ORIGINAL_TEST) - 1:
                self.log(f"\n⏳ انتظار 10 ثواني قبل الثغرة التالية...")
                time.sleep(10)
            
        # طباعة النتائج النهائية
        self.print_final_results()
        
    def print_final_results(self):
        """طباعة النتائج النهائية"""
        self.log("\n" + "="*100)
        self.log("📊 النتائج النهائية لاختبار الدالة الأصلية")
        self.log("="*100)
        
        total_vulnerabilities = len(self.results)
        successful_vulnerabilities = 0
        total_stages_tested = 0
        successful_stages = 0
        
        for result in self.results:
            if result.get('success'):
                vuln_name = result["vulnerability"]["name"]
                stages_success = result.get("successful_stages", 0)
                total_stages = result.get("total_stages", 3)
                
                total_stages_tested += total_stages
                successful_stages += stages_success
                
                if stages_success == total_stages:
                    self.log(f"✅ {vuln_name}: {stages_success}/{total_stages} مراحل نجحت")
                    successful_vulnerabilities += 1
                else:
                    self.log(f"⚠️ {vuln_name}: {stages_success}/{total_stages} مراحل نجحت")
            else:
                error = result.get('error', 'خطأ غير محدد')
                self.log(f"❌ فشل الاختبار: {error}")
                
        self.log(f"\n📈 إحصائيات نهائية:")
        self.log(f"   🎯 الثغرات المختبرة: {total_vulnerabilities}")
        self.log(f"   ✅ الثغرات الناجحة: {successful_vulnerabilities}")
        if total_vulnerabilities > 0:
            self.log(f"   📊 معدل نجاح الثغرات: {(successful_vulnerabilities/total_vulnerabilities)*100:.1f}%")
        if total_stages_tested > 0:
            self.log(f"   📸 إجمالي المراحل: {total_stages_tested}")
            self.log(f"   ✅ المراحل الناجحة: {successful_stages}")
            self.log(f"   📊 معدل نجاح المراحل: {(successful_stages/total_stages_tested)*100:.1f}%")
        
        # فحص مجلد الصور
        screenshots_dir = "screenshots"
        if os.path.exists(screenshots_dir):
            screenshots = []
            for root, dirs, files in os.walk(screenshots_dir):
                for file in files:
                    if file.endswith('.png'):
                        screenshots.append(os.path.join(root, file))
            
            self.log(f"\n📁 مجلد الصور: {len(screenshots)} صورة موجودة")
            
            # عرض أحدث الصور
            if screenshots:
                self.log("\n🖼️ أحدث الصور المُلتقطة:")
                for screenshot in sorted(screenshots)[-15:]:  # آخر 15 صورة
                    file_size = os.path.getsize(screenshot) if os.path.exists(screenshot) else 0
                    self.log(f"   📷 {os.path.basename(screenshot)} ({file_size} bytes)")

def main():
    """الدالة الرئيسية"""
    print("🔥" * 60)
    print("🔥 اختبار الدالة الأصلية للصور الثلاث مع التأثيرات الحقيقية 🔥")
    print("🔥" * 60)
    
    tester = OriginalThreeStagesTester()
    tester.run_original_function_test()
    
    print("\n🎉 انتهى اختبار الدالة الأصلية!")

if __name__ == "__main__":
    main()
