2025-07-26 23:51:49,695 - INFO - 🚀 تم تهيئة خدمة التقاط الصور - الجلسة: e2eefd31
2025-07-26 23:51:49,699 - INFO - 📁 مجلد الصور: E:\agent ai\مجلد جديد\مساعد 2\assets\modules\bugbounty\screenshots
2025-07-26 23:51:49,702 - INFO - ✅ Selenium متوفر
2025-07-26 23:51:49,704 - INFO - ✅ Playwright متوفر
2025-07-26 23:51:50,226 - INFO - ✅ Pillow متوفر
2025-07-26 23:51:50,226 - INFO - ✅ جميع المتطلبات متوفرة
2025-07-26 23:51:52,008 - INFO - ✅ تم تهيئة Playwright browser بنجاح
2025-07-26 23:51:52,009 - INFO - 🎯 التقاط صورة ديناميكية للثغرة: XSS_Test_Before - المرحلة: before
2025-07-26 23:51:52,011 - INFO - 🔗 URL الأصلي للثغرة: https://httpbin.org/html
2025-07-26 23:51:52,014 - INFO - 🔗 URL النهائي للالتقاط: https://httpbin.org/html
2025-07-26 23:51:52,019 - INFO - 🔄 بدء التقاط صورة async للمرحلة: before
2025-07-26 23:51:52,020 - ERROR - ❌ خطأ في التقاط الصورة async: asyncio.run() cannot be called from a running event loop
2025-07-26 23:51:52,024 - ERROR - تفاصيل الخطأ: Traceback (most recent call last):
  File "E:\agent ai\مجلد جديد\مساعد 2\assets\modules\bugbounty\screenshot_service.py", line 1294, in capture_vulnerability_screenshot_dynamic
    result = asyncio.run(self._run_async_capture_with_effects(modified_url, filename, report_id, stage, vulnerability_name, payload_data))
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 190, in run
    raise RuntimeError(
RuntimeError: asyncio.run() cannot be called from a running event loop

2025-07-26 23:51:52,034 - ERROR - ❌ فشل في التقاط صورة الثغرة الديناميكية: خطأ في التقاط الصورة: asyncio.run() cannot be called from a running event loop
2025-07-26 23:51:55,049 - INFO - 🎯 التقاط صورة ديناميكية للثغرة: XSS_Test_During - المرحلة: during
2025-07-26 23:51:55,050 - INFO - 🔗 URL الأصلي للثغرة: https://httpbin.org/html
2025-07-26 23:51:55,050 - INFO - 🔗 URL النهائي للالتقاط: https://httpbin.org/html
2025-07-26 23:51:55,050 - INFO - 🔄 بدء التقاط صورة async للمرحلة: during
2025-07-26 23:51:55,050 - ERROR - ❌ خطأ في التقاط الصورة async: asyncio.run() cannot be called from a running event loop
2025-07-26 23:51:55,051 - ERROR - تفاصيل الخطأ: Traceback (most recent call last):
  File "E:\agent ai\مجلد جديد\مساعد 2\assets\modules\bugbounty\screenshot_service.py", line 1294, in capture_vulnerability_screenshot_dynamic
    result = asyncio.run(self._run_async_capture_with_effects(modified_url, filename, report_id, stage, vulnerability_name, payload_data))
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 190, in run
    raise RuntimeError(
RuntimeError: asyncio.run() cannot be called from a running event loop

2025-07-26 23:51:55,051 - ERROR - ❌ فشل في التقاط صورة الثغرة الديناميكية: خطأ في التقاط الصورة: asyncio.run() cannot be called from a running event loop
2025-07-26 23:51:58,059 - INFO - 🎯 التقاط صورة ديناميكية للثغرة: XSS_Test_After - المرحلة: after
2025-07-26 23:51:58,060 - INFO - 🔗 URL الأصلي للثغرة: https://httpbin.org/html
2025-07-26 23:51:58,060 - INFO - 🔗 URL النهائي للالتقاط: https://httpbin.org/html
2025-07-26 23:51:58,060 - INFO - 🔄 بدء التقاط صورة async للمرحلة: after
2025-07-26 23:51:58,060 - ERROR - ❌ خطأ في التقاط الصورة async: asyncio.run() cannot be called from a running event loop
2025-07-26 23:51:58,061 - ERROR - تفاصيل الخطأ: Traceback (most recent call last):
  File "E:\agent ai\مجلد جديد\مساعد 2\assets\modules\bugbounty\screenshot_service.py", line 1294, in capture_vulnerability_screenshot_dynamic
    result = asyncio.run(self._run_async_capture_with_effects(modified_url, filename, report_id, stage, vulnerability_name, payload_data))
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 190, in run
    raise RuntimeError(
RuntimeError: asyncio.run() cannot be called from a running event loop

2025-07-26 23:51:58,061 - ERROR - ❌ فشل في التقاط صورة الثغرة الديناميكية: خطأ في التقاط الصورة: asyncio.run() cannot be called from a running event loop
2025-07-26 23:51:59,150 - INFO - ✅ تم إغلاق Playwright
