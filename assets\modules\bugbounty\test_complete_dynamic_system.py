#!/usr/bin/env python3
"""
🔥 اختبار شامل للنظام الديناميكي الجديد
اختبار حقيقي للثغرات والتأثيرات البصرية والصور
"""

import json
import time
import requests
from datetime import datetime
import os
import sys

# قائمة الثغرات الحقيقية للاختبار
REAL_VULNERABILITIES = [
    {
        "name": "XSS_Critical_Alert",
        "type": "Cross-Site Scripting",
        "url": "http://testphp.vulnweb.com/artists.php?artist=1",
        "payload": "<script>alert('🔥 XSS CRITICAL VULNERABILITY DETECTED! 🔥')</script>",
        "description": "ثغرة XSS حرجة تسمح بتنفيذ كود JavaScript"
    },
    {
        "name": "SQL_Injection_Database",
        "type": "SQL Injection", 
        "url": "http://testphp.vulnweb.com/listproducts.php?cat=1",
        "payload": "1' UNION SELECT 1,2,3,4,5,6,7,8,9,10,11--",
        "description": "ثغرة SQL Injection تسمح بالوصول لقاعدة البيانات"
    },
    {
        "name": "Command_Injection_System",
        "type": "Command Injection",
        "url": "http://testphp.vulnweb.com/search.php?test=query",
        "payload": "; ls -la; whoami; id;",
        "description": "ثغرة Command Injection تسمح بتنفيذ أوامر النظام"
    },
    {
        "name": "File_Upload_RCE",
        "type": "File Upload",
        "url": "http://testphp.vulnweb.com/upload/",
        "payload": "<?php system($_GET['cmd']); ?>",
        "description": "ثغرة رفع ملفات تسمح بتنفيذ كود PHP"
    },
    {
        "name": "CSRF_Account_Takeover",
        "type": "Cross-Site Request Forgery",
        "url": "http://testphp.vulnweb.com/userinfo.php",
        "payload": "<form action='/userinfo.php' method='POST'><input name='email' value='<EMAIL>'></form>",
        "description": "ثغرة CSRF تسمح بتغيير بيانات المستخدم"
    }
]

# مواقع اختبار إضافية
TEST_SITES = [
    "http://testphp.vulnweb.com",
    "http://demo.testfire.net", 
    "http://zero.webappsecurity.com",
    "https://httpbin.org/html"
]

class CompleteDynamicSystemTester:
    def __init__(self):
        self.base_url = "http://localhost:8000"
        self.results = []
        self.screenshots_dir = "screenshots"
        
    def log(self, message):
        timestamp = datetime.now().strftime("%H:%M:%S")
        print(f"[{timestamp}] {message}")
        
    def test_server_connection(self):
        """اختبار الاتصال بالسيرفر"""
        try:
            # اختبار endpoint capture مباشرة
            test_payload = {
                "url": "https://httpbin.org/html",
                "report_id": "connection_test"
            }
            response = requests.post(f"{self.base_url}/capture", json=test_payload, timeout=120)
            if response.status_code in [200, 400, 500]:  # أي رد يعني أن السيرفر يعمل
                self.log("✅ السيرفر يعمل بشكل صحيح")
                return True
            else:
                self.log(f"❌ السيرفر يرد بكود: {response.status_code}")
                return False
        except requests.exceptions.ConnectionError:
            self.log("❌ لا يمكن الاتصال بالسيرفر - تأكد من تشغيل python_web_service.py")
            return False
        except Exception as e:
            self.log(f"❌ خطأ في الاتصال: {e}")
            return False
            
    def capture_vulnerability_screenshot(self, vuln_data, stage="after"):
        """التقاط صورة للثغرة مع التأثيرات البصرية"""
        try:
            self.log(f"🔥 اختبار {vuln_data['name']} - مرحلة {stage}")

            payload = {
                "url": vuln_data["url"],
                "report_id": f"{vuln_data['name']}_{stage}_{int(time.time())}",
                "vulnerability_name": vuln_data["name"],
                "vulnerability_type": vuln_data["type"],
                "stage": stage,
                "payload": vuln_data.get("payload", ""),
                "width": 1920,
                "height": 1080,
                "wait_time": 5
            }

            self.log(f"📤 إرسال طلب إلى: {self.base_url}/capture")
            self.log(f"📋 البيانات: {json.dumps(payload, indent=2)}")

            response = requests.post(
                f"{self.base_url}/capture",
                json=payload,
                timeout=60  # زيادة المهلة الزمنية
            )

            self.log(f"📥 رد السيرفر: {response.status_code}")

            if response.status_code == 200:
                try:
                    result = response.json()
                    screenshot_path = result.get('screenshot_path', 'غير محدد')
                    self.log(f"✅ نجح التقاط الصورة: {screenshot_path}")

                    # التحقق من وجود الملف
                    if os.path.exists(screenshot_path):
                        self.log(f"📁 الملف موجود: {screenshot_path}")
                    else:
                        self.log(f"⚠️ الملف غير موجود: {screenshot_path}")

                    return result
                except json.JSONDecodeError:
                    self.log(f"❌ خطأ في تحليل JSON: {response.text[:200]}")
                    return None
            else:
                self.log(f"❌ فشل التقاط الصورة: {response.status_code}")
                self.log(f"📄 Response: {response.text[:500]}")
                return None

        except requests.exceptions.Timeout:
            self.log(f"⏰ انتهت مهلة الطلب للثغرة {vuln_data['name']}")
            return None
        except Exception as e:
            self.log(f"❌ خطأ في التقاط الصورة: {e}")
            return None
            
    def test_before_after_comparison(self, vuln_data):
        """اختبار مقارنة قبل وبعد الاستغلال"""
        self.log(f"\n🎯 اختبار مقارنة قبل/بعد للثغرة: {vuln_data['name']}")
        
        # التقاط صورة قبل الاستغلال
        before_result = self.capture_vulnerability_screenshot(vuln_data, "before")
        time.sleep(2)
        
        # التقاط صورة بعد الاستغلال
        after_result = self.capture_vulnerability_screenshot(vuln_data, "after")
        
        return {
            "vulnerability": vuln_data,
            "before_screenshot": before_result,
            "after_screenshot": after_result,
            "comparison_successful": before_result is not None and after_result is not None
        }
        
    def test_visual_effects_system(self):
        """اختبار نظام التأثيرات البصرية"""
        self.log("\n🌟 اختبار نظام التأثيرات البصرية الديناميكي")
        
        effects_test = {
            "name": "Visual_Effects_Test",
            "type": "Cross-Site Scripting",
            "url": "https://httpbin.org/html",
            "payload": "<script>alert('🔥 VISUAL EFFECTS TEST 🔥')</script>",
            "description": "اختبار التأثيرات البصرية الديناميكية"
        }
        
        return self.capture_vulnerability_screenshot(effects_test, "after")
        
    def run_comprehensive_test(self):
        """تشغيل الاختبار الشامل"""
        self.log("🚀 بدء الاختبار الشامل للنظام الديناميكي")

        # اختبار الاتصال
        if not self.test_server_connection():
            self.log("❌ فشل الاختبار - السيرفر لا يعمل")
            self.log("💡 تأكد من تشغيل: python python_web_service.py")
            return False

        self.log("✅ السيرفر متصل - بدء الاختبارات...")

        # اختبار التأثيرات البصرية أولاً
        self.log("\n🌟 اختبار التأثيرات البصرية...")
        visual_test = self.test_visual_effects_system()
        if visual_test:
            self.log("✅ اختبار التأثيرات البصرية نجح")
        else:
            self.log("❌ اختبار التأثيرات البصرية فشل")

        # اختبار عينة من الثغرات (أول 3 فقط لتوفير الوقت)
        self.log(f"\n🎯 اختبار {min(3, len(REAL_VULNERABILITIES))} ثغرات...")
        for i, vuln in enumerate(REAL_VULNERABILITIES[:3]):
            self.log(f"\n--- اختبار {i+1}/3 ---")
            result = self.test_before_after_comparison(vuln)
            self.results.append(result)
            time.sleep(2)  # انتظار أقل بين الاختبارات

        # طباعة النتائج النهائية
        self.print_final_results()
        
    def print_final_results(self):
        """طباعة النتائج النهائية"""
        self.log("\n" + "="*60)
        self.log("📊 النتائج النهائية للاختبار الشامل")
        self.log("="*60)
        
        successful_tests = 0
        total_tests = len(self.results)
        
        for result in self.results:
            vuln_name = result["vulnerability"]["name"]
            success = result["comparison_successful"]
            
            if success:
                self.log(f"✅ {vuln_name}: نجح الاختبار")
                successful_tests += 1
            else:
                self.log(f"❌ {vuln_name}: فشل الاختبار")
                
        self.log(f"\n📈 معدل النجاح: {successful_tests}/{total_tests} ({(successful_tests/total_tests)*100:.1f}%)")
        
        # فحص مجلد الصور
        if os.path.exists(self.screenshots_dir):
            screenshots = [f for f in os.listdir(self.screenshots_dir) if f.endswith('.png')]
            self.log(f"📸 تم إنشاء {len(screenshots)} صورة في مجلد {self.screenshots_dir}")
            
            # عرض أحدث الصور
            if screenshots:
                self.log("\n🖼️ أحدث الصور المُلتقطة:")
                for screenshot in sorted(screenshots)[-5:]:  # آخر 5 صور
                    self.log(f"   📷 {screenshot}")
        else:
            self.log("⚠️ مجلد الصور غير موجود")

def main():
    """الدالة الرئيسية"""
    print("🔥" * 30)
    print("🔥 اختبار النظام الديناميكي الشامل 🔥")
    print("🔥" * 30)
    
    tester = CompleteDynamicSystemTester()
    tester.run_comprehensive_test()
    
    print("\n🎉 انتهى الاختبار الشامل!")

if __name__ == "__main__":
    main()
