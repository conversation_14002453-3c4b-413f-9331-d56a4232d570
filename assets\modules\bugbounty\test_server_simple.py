#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import traceback

try:
    print("🔍 اختبار استيراد الوحدات...")
    
    # اختبار الاستيرادات الأساسية
    from flask import Flask, request, jsonify
    print("✅ Flask imported")
    
    from flask_cors import CORS
    print("✅ CORS imported")
    
    import threading
    print("✅ threading imported")
    
    from collections import defaultdict
    print("✅ defaultdict imported")
    
    import time
    print("✅ time imported")
    
    # اختبار استيراد screenshot_service
    try:
        from screenshot_service import ScreenshotService
        print("✅ ScreenshotService imported")
    except Exception as e:
        print(f"❌ خطأ في استيراد ScreenshotService: {e}")
    
    print("🎉 جميع الاستيرادات نجحت!")
    
    # اختبار إنشاء Flask app
    app = Flask(__name__)
    CORS(app)
    print("✅ Flask app created")
    
    print("🚀 السيرفر جاهز للتشغيل!")
    
except Exception as e:
    print(f"❌ خطأ في الاختبار: {e}")
    print("📋 تفاصيل الخطأ:")
    traceback.print_exc()
