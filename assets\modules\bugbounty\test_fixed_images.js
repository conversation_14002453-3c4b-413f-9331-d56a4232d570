// اختبار الإصلاحات الجديدة لعرض الصور في التقارير

console.log('🔥 اختبار الإصلاحات الجديدة لعرض الصور في التقارير');

// محاكاة بيانات الثغرة مع الصور الموجودة فعلياً
const testVulnerability = {
    name: 'XSS Cross Site Scripting',
    type: 'XSS',
    severity: 'High',
    screenshots: {
        before: './assets/modules/bugbounty/screenshots/real_report_test_1753369845/single_before_XSS_REPORT_TEST_1.png',
        during: './assets/modules/bugbounty/screenshots/real_report_test_1753369845/single_during_XSS_REPORT_TEST_1.png',
        after: './assets/modules/bugbounty/screenshots/real_report_test_1753369845/single_after_XSS_REPORT_TEST_1.png'
    },
    visual_proof: {
        before_screenshot_path: './assets/modules/bugbounty/screenshots/real_report_test_1753369845/single_before_XSS_REPORT_TEST_1.png',
        during_screenshot_path: './assets/modules/bugbounty/screenshots/real_report_test_1753369845/single_during_XSS_REPORT_TEST_1.png',
        after_screenshot_path: './assets/modules/bugbounty/screenshots/real_report_test_1753369845/single_after_XSS_REPORT_TEST_1.png'
    }
};

// محاكاة النظام v4
class MockBugBountyCore {
    constructor() {
        this.analysisState = {
            currentScanFolder: 'real_report_test_1753369845'
        };
    }

    // 🔥 دالة البحث عن مسارات الصور الحقيقية للثغرة - متوافقة مع النظام v4 الديناميكي
    findRealImageForVulnerability(vuln, stage) {
        console.log(`🔍 البحث عن مسار صورة ${stage} للثغرة: ${vuln.name}`);

        // 🔥 إصلاح v4: البحث في مسارات الصور المحفوظة أولاً (من النظام v4)
        if (vuln.screenshots && vuln.screenshots[stage]) {
            const savedPath = vuln.screenshots[stage];
            if (typeof savedPath === 'string' && savedPath.includes('./assets/modules/bugbounty/screenshots/')) {
                console.log(`✅ تم العثور على مسار صورة ${stage} محفوظ من النظام v4: ${savedPath}`);
                return savedPath;
            }
        }

        // البحث في visual_proof paths (من النظام v4)
        if (vuln.visual_proof && vuln.visual_proof[`${stage}_screenshot_path`]) {
            const savedPath = vuln.visual_proof[`${stage}_screenshot_path`];
            console.log(`✅ تم العثور على مسار صورة ${stage} في visual_proof: ${savedPath}`);
            return savedPath;
        }

        // 🔥 إصلاح v4: البحث الديناميكي في المجلدات الحديثة
        const dynamicImagePath = this.findDynamicImagePath(vuln, stage);
        if (dynamicImagePath) {
            console.log(`✅ تم العثور على صورة ديناميكية: ${dynamicImagePath}`);
            return dynamicImagePath;
        }

        console.log(`❌ لم يتم العثور على صورة للمرحلة ${stage}`);
        return null;
    }

    // 🔥 دالة جديدة v4: البحث الديناميكي عن مسارات الصور في المجلدات الحديثة
    findDynamicImagePath(vuln, stage) {
        console.log(`🔍 البحث الديناميكي عن صورة ${stage} للثغرة: ${vuln.name}`);

        // محاكاة البحث في المجلدات
        const screenshotsDir = './assets/modules/bugbounty/screenshots/';
        const folderName = this.analysisState.currentScanFolder;
        
        if (folderName) {
            // محاكاة الملفات الموجودة
            const mockFiles = [
                'single_before_XSS_REPORT_TEST_1.png',
                'single_during_XSS_REPORT_TEST_1.png', 
                'single_after_XSS_REPORT_TEST_1.png',
                'single_before_SQL_Injection_REPORT_TEST_2.png',
                'single_during_SQL_Injection_REPORT_TEST_2.png',
                'single_after_SQL_Injection_REPORT_TEST_2.png'
            ];
            
            // البحث عن الصورة المطلوبة
            const imageFile = this.findMatchingImageInFolder(mockFiles, vuln, stage);
            
            if (imageFile) {
                const imagePath = `${screenshotsDir}${folderName}/${imageFile}`;
                console.log(`✅ تم العثور على صورة ديناميكية: ${imagePath}`);
                return imagePath;
            }
        }

        return null;
    }

    // 🔥 دالة مساعدة: البحث عن الصورة المطابقة في المجلد
    findMatchingImageInFolder(files, vuln, stage) {
        console.log(`🔍 البحث في ${files.length} ملف عن صورة ${stage}`);

        // أنماط البحث المختلفة - محدثة للنظام v4
        const searchPatterns = [
            // 🔥 النمط الجديد v4: single_stage_VulnType_REPORT_TEST.png
            `single_${stage}_${vuln.type}_REPORT_TEST_1.png`,
            `single_${stage}_${vuln.type}_REPORT_TEST_2.png`,
            `single_${stage}_${vuln.type}_REAL.png`,
            `single_${stage}_${vuln.type}_TEST.png`,
            
            // النمط القديم: stage_VulnType_REAL.png
            `${stage}_${vuln.type}_REAL.png`,
            `${stage}_${vuln.type}_REAL_TEST.png`,
            
            // أنماط عامة مع single_
            `single_${stage}_XSS_REAL.png`,
            `single_${stage}_SQL_Injection_REAL.png`
        ];

        console.log(`🎯 أنماط البحث: ${searchPatterns.slice(0, 3).join(', ')}...`);
        
        // البحث بالأنماط المختلفة
        for (const pattern of searchPatterns) {
            const matchingFile = files.find(file => file === pattern);
            if (matchingFile) {
                console.log(`✅ تم العثور على صورة مطابقة: ${matchingFile}`);
                return matchingFile;
            }
        }

        // البحث الجزئي (يحتوي على stage و نوع الثغرة)
        const partialMatch = files.find(file => {
            const fileName = file.toLowerCase();
            const stageMatch = fileName.includes(stage.toLowerCase());
            const typeMatch = vuln.type && fileName.includes(vuln.type.toLowerCase().replace(/\s+/g, '_'));
            return stageMatch && typeMatch && fileName.endsWith('.png');
        });

        if (partialMatch) {
            console.log(`✅ تم العثور على صورة مطابقة جزئياً: ${partialMatch}`);
            return partialMatch;
        }

        console.log(`❌ لم يتم العثور على صورة مطابقة للمرحلة ${stage}`);
        return null;
    }
}

// اختبار النظام
console.log('\n🧪 اختبار النظام المُصلح:');
console.log('=' * 50);

const mockCore = new MockBugBountyCore();

// اختبار البحث عن الصور
const stages = ['before', 'during', 'after'];

stages.forEach(stage => {
    console.log(`\n📸 اختبار البحث عن صورة ${stage}:`);
    const imagePath = mockCore.findRealImageForVulnerability(testVulnerability, stage);
    
    if (imagePath) {
        console.log(`   ✅ تم العثور على الصورة: ${imagePath}`);
    } else {
        console.log(`   ❌ لم يتم العثور على الصورة`);
    }
});

console.log('\n🎉 انتهى الاختبار!');
