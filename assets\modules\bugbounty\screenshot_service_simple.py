#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🔥 النظام الديناميكي الشامل v4 - خدمة التقاط الصور
تم حذف جميع الشروط اليدوية والاعتماد على النظام الديناميكي 100%
"""

import asyncio
import logging
import time
import os
import hashlib
from pathlib import Path

# إعداد التسجيل
logging.basicConfig(level=logging.INFO, format='%(levelname)s:%(name)s:%(message)s')
logger = logging.getLogger(__name__)

class ScreenshotService:
    def __init__(self):
        self.browser = None
        self.page = None
        logger.info("🚀 تم تهيئة خدمة التقاط الصور الديناميكية v4")

    async def initialize_browser(self):
        """تهيئة المتصفح"""
        try:
            from playwright.async_api import async_playwright
            self.playwright = await async_playwright().start()
            self.browser = await self.playwright.chromium.launch(
                headless=True,
                args=['--no-sandbox', '--disable-setuid-sandbox']
            )
            logger.info("✅ تم تهيئة المتصفح بنجاح")
            return True
        except Exception as e:
            logger.error(f"❌ فشل في تهيئة المتصفح: {e}")
            return False

    async def capture_vulnerability_screenshot(self, url, vulnerability_name, vulnerability_type, stage, payload_data=None):
        """
        🔥 النظام الديناميكي الشامل - التقاط صور لجميع الثغرات
        """
        try:
            logger.info(f"🎯 بدء التقاط صورة ديناميكية: {vulnerability_name} ({stage})")
            
            # إنشاء صفحة جديدة
            page = await self.browser.new_page()
            
            # الانتقال للصفحة
            await page.goto(url, wait_until='networkidle', timeout=30000)
            
            # 🔥 النظام الديناميكي - تطبيق تأثيرات حسب المرحلة
            if stage == 'before':
                await self._apply_before_effects(page, vulnerability_name)
            elif stage == 'during':
                await self._apply_during_effects(page, vulnerability_name, payload_data)
            elif stage == 'after':
                await self._apply_after_effects(page, vulnerability_name, payload_data)
            
            # انتظار قصير للتأثيرات
            await page.wait_for_timeout(2000)
            
            # التقاط الصورة
            screenshot_path = self._get_screenshot_path(vulnerability_name, stage)
            await page.screenshot(path=screenshot_path, full_page=True)
            
            await page.close()
            logger.info(f"✅ تم التقاط الصورة: {screenshot_path}")
            return True
            
        except Exception as e:
            logger.error(f"❌ فشل في التقاط الصورة: {e}")
            return False

    async def _apply_before_effects(self, page, vulnerability_name):
        """تطبيق تأثيرات مرحلة ما قبل الاستغلال"""
        await page.evaluate(f"""
            document.body.style.outline = '3px solid green';
            const watermark = document.createElement('div');
            watermark.innerHTML = '📋 قبل الاستغلال - {vulnerability_name}';
            watermark.style.cssText = `
                position: fixed; top: 10px; right: 10px; z-index: 10000;
                background: rgba(0,255,0,0.8); color: white; padding: 5px 10px;
                font-size: 12px; border-radius: 5px; font-family: Arial, sans-serif;
            `;
            document.body.appendChild(watermark);
        """)

    async def _apply_during_effects(self, page, vulnerability_name, payload_data):
        """🔥 النظام الديناميكي - تطبيق تأثيرات أثناء الاستغلال"""
        # إنشاء تأثيرات ديناميكية حسب hash الثغرة
        vuln_hash = hash(f"{vulnerability_name}")
        hue = abs(vuln_hash) % 360
        
        await page.evaluate(f"""
            const vulnerabilityName = '{vulnerability_name}';
            const payload = '{payload_data or "DYNAMIC_PAYLOAD"}';
            
            // تأثيرات ديناميكية حسب الثغرة
            const hue = {hue};
            document.body.style.background = `linear-gradient(45deg, 
                hsl(${{hue}}, 70%, 50%), 
                hsl(${{hue + 60}}, 70%, 60%))`;
            document.body.style.outline = `10px solid hsl(${{hue}}, 70%, 40%)`;
            
            // إضافة تنبيه ديناميكي
            const alert = document.createElement('div');
            alert.innerHTML = `
                <h1 style="color: hsl(${{hue}}, 70%, 30%);">🚨 ${{vulnerabilityName}} EXPLOITED! 🚨</h1>
                <div style="background: #000; color: hsl(${{hue}}, 70%, 80%); padding: 15px; margin: 10px; border-radius: 10px;">
                    <p><strong>VULNERABILITY:</strong> ${{vulnerabilityName}}</p>
                    <p><strong>PAYLOAD:</strong> ${{payload}}</p>
                    <p><strong>STATUS:</strong> ✅ EXPLOITATION IN PROGRESS</p>
                </div>
            `;
            alert.style.cssText = `
                position: fixed; top: 20px; left: 20px; right: 20px; z-index: 10000;
                background: rgba(255,255,255,0.95); padding: 20px; border-radius: 15px;
                text-align: center; border: 5px solid hsl(${{hue}}, 70%, 40%);
            `;
            document.body.appendChild(alert);
        """)

    async def _apply_after_effects(self, page, vulnerability_name, payload_data):
        """🔥 النظام الديناميكي - تطبيق تأثيرات نهائية"""
        vuln_hash = hash(f"{vulnerability_name}_final")
        hue = abs(vuln_hash) % 360
        
        await page.evaluate(f"""
            const vulnerabilityName = '{vulnerability_name}';
            const payload = '{payload_data or "DYNAMIC_PAYLOAD"}';
            
            // تأثيرات نهائية قوية
            const hue = {hue};
            document.body.style.background = `linear-gradient(45deg, 
                hsl(${{hue}}, 80%, 30%), 
                hsl(${{hue + 90}}, 80%, 50%))`;
            document.body.style.outline = `15px solid hsl(${{hue}}, 80%, 20%)`;
            document.body.style.animation = 'final-pulse 1s infinite';
            
            // CSS للتأثيرات المتحركة
            const style = document.createElement('style');
            style.textContent = `
                @keyframes final-pulse {{
                    0% {{ filter: brightness(1); }}
                    50% {{ filter: brightness(1.3); }}
                    100% {{ filter: brightness(1); }}
                }}
            `;
            document.head.appendChild(style);
            
            // تنبيه نهائي كبير
            const finalAlert = document.createElement('div');
            finalAlert.innerHTML = `
                <h1 style="color: hsl(${{hue}}, 80%, 20%); font-size: 36px;">🔥 ${{vulnerabilityName}} FULLY EXPLOITED! 🔥</h1>
                <div style="background: #000; color: hsl(${{hue}}, 80%, 80%); padding: 25px; margin: 15px; border-radius: 15px;">
                    <h2>FINAL EXPLOITATION RESULTS:</h2>
                    <p><strong>VULNERABILITY:</strong> ${{vulnerabilityName}}</p>
                    <p><strong>PAYLOAD:</strong> ${{payload}}</p>
                    <p><strong>STATUS:</strong> ✅ COMPLETE EXPLOITATION</p>
                    <p><strong>IMPACT:</strong> 🔥 SECURITY BREACH CONFIRMED</p>
                    <p><strong>TIMESTAMP:</strong> ${{new Date().toLocaleString()}}</p>
                </div>
            `;
            finalAlert.style.cssText = `
                position: fixed; top: 30px; left: 30px; right: 30px; z-index: 10000;
                background: rgba(255,255,255,0.95); padding: 25px; border-radius: 20px;
                text-align: center; border: 8px solid hsl(${{hue}}, 80%, 20%);
                box-shadow: 0 0 50px hsl(${{hue}}, 80%, 50%);
            `;
            document.body.appendChild(finalAlert);
        """)

    def _get_screenshot_path(self, vulnerability_name, stage):
        """إنشاء مسار الصورة"""
        screenshots_dir = Path("screenshots")
        screenshots_dir.mkdir(exist_ok=True)
        
        timestamp = int(time.time())
        filename = f"{stage}_{vulnerability_name}_{timestamp}.png"
        return screenshots_dir / filename

    async def close(self):
        """إغلاق المتصفح"""
        if self.browser:
            await self.browser.close()
        if hasattr(self, 'playwright'):
            await self.playwright.stop()
        logger.info("🔒 تم إغلاق خدمة التقاط الصور")
