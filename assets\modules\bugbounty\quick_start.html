<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🚀 تشغيل سريع - نظام Bug Bounty v4 مع Python</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
            direction: rtl;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .quick-actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        
        .action-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 25px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }
        
        .action-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }
        
        .action-card h3 {
            font-size: 1.5em;
            margin-bottom: 15px;
            color: #fff;
        }
        
        .action-card p {
            margin-bottom: 20px;
            opacity: 0.9;
            line-height: 1.6;
        }
        
        .btn {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: all 0.3s ease;
            width: 100%;
            margin: 5px 0;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }
        
        .btn.secondary {
            background: linear-gradient(45deg, #3742fa, #2f3542);
        }
        
        .btn.success {
            background: linear-gradient(45deg, #2ed573, #1e90ff);
        }
        
        .status-panel {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 25px;
            backdrop-filter: blur(10px);
            margin-bottom: 20px;
        }
        
        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .status-item:last-child {
            border-bottom: none;
        }
        
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: 10px;
        }
        
        .status-ok { background: #2ed573; }
        .status-warning { background: #ffa502; }
        .status-error { background: #ff3838; }
        .status-unknown { background: #747d8c; }
        
        .input-group {
            margin: 15px 0;
        }
        
        .input-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        
        .input-group input, .input-group select {
            width: 100%;
            padding: 10px;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            background: rgba(255, 255, 255, 0.9);
            color: #333;
        }
        
        .log-panel {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
            font-family: 'Courier New', monospace;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        
        .progress-bar {
            width: 100%;
            height: 20px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(45deg, #2ed573, #1e90ff);
            width: 0%;
            transition: width 0.3s ease;
        }
        
        .icon {
            font-size: 2em;
            margin-bottom: 15px;
            display: block;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 نظام Bug Bounty v4 مع Python</h1>
            <p>تشغيل سريع وسهل للنظام المتكامل مع التقاط الصور الحقيقية</p>
        </div>

        <!-- حالة النظام -->
        <div class="status-panel">
            <h3>📊 حالة النظام</h3>
            <div class="status-item">
                <span>خدمة Python</span>
                <div style="display: flex; align-items: center;">
                    <span id="pythonStatus">جاري الفحص...</span>
                    <div class="status-indicator status-unknown" id="pythonIndicator"></div>
                </div>
            </div>
            <div class="status-item">
                <span>جسر JavaScript</span>
                <div style="display: flex; align-items: center;">
                    <span id="bridgeStatus">جاري الفحص...</span>
                    <div class="status-indicator status-unknown" id="bridgeIndicator"></div>
                </div>
            </div>
            <div class="status-item">
                <span>نظام Bug Bounty Core</span>
                <div style="display: flex; align-items: center;">
                    <span id="coreStatus">جاري الفحص...</span>
                    <div class="status-indicator status-unknown" id="coreIndicator"></div>
                </div>
            </div>
            <div class="status-item">
                <span>متصفحات النظام</span>
                <div style="display: flex; align-items: center;">
                    <span id="browserStatus">جاري الفحص...</span>
                    <div class="status-indicator status-unknown" id="browserIndicator"></div>
                </div>
            </div>
        </div>

        <!-- الإجراءات السريعة -->
        <div class="quick-actions">
            <!-- فحص سريع -->
            <div class="action-card">
                <span class="icon">🔍</span>
                <h3>فحص سريع</h3>
                <p>ابدأ فحص أمني سريع لموقع واحد مع التقاط الصور</p>
                
                <div class="input-group">
                    <label>رابط الموقع:</label>
                    <input type="url" id="quickScanUrl" placeholder="https://example.com" value="https://example.com">
                </div>
                
                <button class="btn" onclick="startQuickScan()">بدء الفحص السريع</button>
                <button class="btn secondary" onclick="captureQuickScreenshot()">التقاط صورة فقط</button>
            </div>

            <!-- فحص شامل -->
            <div class="action-card">
                <span class="icon">🛡️</span>
                <h3>فحص شامل</h3>
                <p>فحص أمني متقدم مع اختبار الثغرات وتوليد التقارير</p>
                
                <div class="input-group">
                    <label>رابط الموقع:</label>
                    <input type="url" id="comprehensiveScanUrl" placeholder="https://example.com">
                </div>
                
                <div class="input-group">
                    <label>نوع الفحص:</label>
                    <select id="scanType">
                        <option value="basic">أساسي</option>
                        <option value="advanced">متقدم</option>
                        <option value="comprehensive">شامل</option>
                    </select>
                </div>
                
                <button class="btn success" onclick="startComprehensiveScan()">بدء الفحص الشامل</button>
            </div>

            <!-- اختبار النظام -->
            <div class="action-card">
                <span class="icon">🧪</span>
                <h3>اختبار النظام</h3>
                <p>اختبار جميع مكونات النظام والتأكد من عملها</p>
                
                <button class="btn secondary" onclick="runSystemTest()">اختبار شامل</button>
                <button class="btn secondary" onclick="testPythonService()">اختبار خدمة Python</button>
                <button class="btn secondary" onclick="testScreenshotCapture()">اختبار التقاط الصور</button>
                <button class="btn secondary" onclick="openTestPage()">فتح صفحة الاختبار</button>
            </div>

            <!-- إدارة النظام -->
            <div class="action-card">
                <span class="icon">⚙️</span>
                <h3>إدارة النظام</h3>
                <p>إعدادات وأدوات إدارة النظام</p>
                
                <button class="btn secondary" onclick="viewSystemStats()">عرض الإحصائيات</button>
                <button class="btn secondary" onclick="clearSystemCache()">مسح الذاكرة المؤقتة</button>
                <button class="btn secondary" onclick="exportSystemLogs()">تصدير السجلات</button>
                <button class="btn secondary" onclick="openDocumentation()">فتح التوثيق</button>
            </div>
        </div>

        <!-- شريط التقدم -->
        <div id="progressSection" style="display: none;">
            <h3>📈 تقدم العملية</h3>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            <p id="progressText">جاري التحضير...</p>
        </div>

        <!-- سجل الأحداث -->
        <div class="log-panel" id="systemLog">
            🚀 مرحباً بك في نظام Bug Bounty v4 مع تكامل Python
            📋 جاري تحميل النظام وفحص المكونات...
        </div>
    </div>

    <!-- تحميل الملفات -->
    <script src="./python_screenshot_bridge.js"></script>
    <script src="./BugBountyCore.js"></script>

    <script>
        let pythonBridge = null;
        let bugBountyCore = null;
        let systemReady = false;

        // تهيئة النظام
        async function initializeSystem() {
            log('🔧 بدء تهيئة النظام...');
            
            try {
                // تحميل جسر Python
                if (typeof PythonScreenshotBridge !== 'undefined') {
                    pythonBridge = new PythonScreenshotBridge();
                    updateStatus('bridge', 'ok', 'متصل');
                    log('✅ تم تحميل جسر Python بنجاح');
                } else {
                    updateStatus('bridge', 'error', 'غير متوفر');
                    log('❌ فشل في تحميل جسر Python');
                }

                // تحميل Bug Bounty Core
                if (typeof BugBountyCore !== 'undefined') {
                    bugBountyCore = new BugBountyCore();
                    updateStatus('core', 'ok', 'جاهز');
                    log('✅ تم تحميل Bug Bounty Core بنجاح');
                } else {
                    updateStatus('core', 'error', 'غير متوفر');
                    log('❌ فشل في تحميل Bug Bounty Core');
                }

                // فحص خدمة Python
                await checkPythonService();

                // فحص المتصفحات
                await checkBrowsers();

                systemReady = true;
                log('🎉 النظام جاهز للاستخدام!');

            } catch (error) {
                log(`❌ خطأ في تهيئة النظام: ${error.message}`);
                systemReady = false;
            }
        }

        // فحص خدمة Python
        async function checkPythonService() {
            try {
                if (pythonBridge) {
                    const health = await pythonBridge.checkHealth();
                    if (health.healthy) {
                        updateStatus('python', 'ok', 'يعمل');
                        log('✅ خدمة Python تعمل بشكل صحيح');
                    } else {
                        updateStatus('python', 'warning', 'تحذيرات');
                        log(`⚠️ خدمة Python: ${health.message}`);
                    }
                } else {
                    updateStatus('python', 'error', 'غير متوفر');
                }
            } catch (error) {
                updateStatus('python', 'error', 'خطأ');
                log(`❌ خطأ في فحص خدمة Python: ${error.message}`);
            }
        }

        // فحص المتصفحات
        async function checkBrowsers() {
            try {
                updateStatus('browser', 'ok', 'متوفرة');
                log('✅ متصفحات النظام متوفرة');
            } catch (error) {
                updateStatus('browser', 'warning', 'محدودة');
                log(`⚠️ بعض المتصفحات قد لا تكون متوفرة`);
            }
        }

        // تحديث حالة المكون
        function updateStatus(component, status, text) {
            const statusElement = document.getElementById(`${component}Status`);
            const indicatorElement = document.getElementById(`${component}Indicator`);
            
            if (statusElement) statusElement.textContent = text;
            if (indicatorElement) {
                indicatorElement.className = `status-indicator status-${status}`;
            }
        }

        // بدء فحص سريع
        async function startQuickScan() {
            const url = document.getElementById('quickScanUrl').value;
            if (!url) {
                log('❌ يرجى إدخال رابط صحيح');
                return;
            }

            if (!systemReady) {
                log('❌ النظام غير جاهز بعد');
                return;
            }

            log(`🔍 بدء فحص سريع للموقع: ${url}`);
            showProgress(true);
            
            try {
                updateProgress(20, 'جاري التحضير...');
                
                if (bugBountyCore) {
                    updateProgress(50, 'جاري الفحص...');
                    // هنا يمكن إضافة منطق الفحص السريع
                    await new Promise(resolve => setTimeout(resolve, 2000));
                    
                    updateProgress(80, 'جاري التقاط الصور...');
                    const screenshot = await bugBountyCore.captureWebsiteScreenshotV4(url, 'quick_scan');
                    
                    updateProgress(100, 'تم الانتهاء!');
                    log('✅ تم الفحص السريع بنجاح');
                } else {
                    log('❌ Bug Bounty Core غير متوفر');
                }
            } catch (error) {
                log(`❌ خطأ في الفحص السريع: ${error.message}`);
            } finally {
                showProgress(false);
            }
        }

        // التقاط صورة سريعة
        async function captureQuickScreenshot() {
            const url = document.getElementById('quickScanUrl').value;
            if (!url) {
                log('❌ يرجى إدخال رابط صحيح');
                return;
            }

            log(`📸 التقاط صورة للموقع: ${url}`);
            
            try {
                if (pythonBridge) {
                    const result = await pythonBridge.captureWebsiteScreenshot(url, 'quick_screenshot');
                    if (result.success) {
                        log('✅ تم التقاط الصورة بنجاح');
                    } else {
                        log(`❌ فشل في التقاط الصورة: ${result.error}`);
                    }
                } else {
                    log('❌ جسر Python غير متوفر');
                }
            } catch (error) {
                log(`❌ خطأ في التقاط الصورة: ${error.message}`);
            }
        }

        // بدء فحص شامل
        async function startComprehensiveScan() {
            const url = document.getElementById('comprehensiveScanUrl').value;
            const scanType = document.getElementById('scanType').value;
            
            if (!url) {
                log('❌ يرجى إدخال رابط صحيح');
                return;
            }

            log(`🛡️ بدء فحص شامل (${scanType}) للموقع: ${url}`);
            showProgress(true);
            
            try {
                updateProgress(10, 'جاري التحضير...');
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                updateProgress(30, 'جاري اكتشاف الثغرات...');
                await new Promise(resolve => setTimeout(resolve, 2000));
                
                updateProgress(60, 'جاري اختبار الاستغلال...');
                await new Promise(resolve => setTimeout(resolve, 2000));
                
                updateProgress(90, 'جاري إنشاء التقرير...');
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                updateProgress(100, 'تم الانتهاء!');
                log('✅ تم الفحص الشامل بنجاح');
                
            } catch (error) {
                log(`❌ خطأ في الفحص الشامل: ${error.message}`);
            } finally {
                showProgress(false);
            }
        }

        // تشغيل اختبار النظام
        async function runSystemTest() {
            log('🧪 بدء اختبار شامل للنظام...');
            
            try {
                await checkPythonService();
                await testPythonService();
                await testScreenshotCapture();
                log('✅ اكتمل اختبار النظام بنجاح');
            } catch (error) {
                log(`❌ خطأ في اختبار النظام: ${error.message}`);
            }
        }

        // اختبار خدمة Python
        async function testPythonService() {
            log('🐍 اختبار خدمة Python...');
            
            try {
                if (pythonBridge) {
                    const stats = await pythonBridge.getStatistics();
                    log(`✅ إحصائيات Python: ${JSON.stringify(stats)}`);
                } else {
                    log('❌ خدمة Python غير متوفرة');
                }
            } catch (error) {
                log(`❌ خطأ في اختبار Python: ${error.message}`);
            }
        }

        // اختبار التقاط الصور
        async function testScreenshotCapture() {
            log('📸 اختبار التقاط الصور...');
            
            try {
                if (pythonBridge) {
                    const result = await pythonBridge.captureWebsiteScreenshot('https://example.com', 'test');
                    if (result.success) {
                        log('✅ اختبار التقاط الصور نجح');
                    } else {
                        log(`❌ فشل اختبار التقاط الصور: ${result.error}`);
                    }
                } else {
                    log('❌ جسر Python غير متوفر للاختبار');
                }
            } catch (error) {
                log(`❌ خطأ في اختبار التقاط الصور: ${error.message}`);
            }
        }

        // فتح صفحة الاختبار
        function openTestPage() {
            window.open('./test_python_integration.html', '_blank');
        }

        // عرض الإحصائيات
        async function viewSystemStats() {
            log('📊 جمع إحصائيات النظام...');
            
            try {
                if (pythonBridge) {
                    const stats = await pythonBridge.getStatistics();
                    log(`📊 الإحصائيات: ${JSON.stringify(stats, null, 2)}`);
                } else {
                    log('❌ لا يمكن جمع الإحصائيات - جسر Python غير متوفر');
                }
            } catch (error) {
                log(`❌ خطأ في جمع الإحصائيات: ${error.message}`);
            }
        }

        // مسح الذاكرة المؤقتة
        function clearSystemCache() {
            log('🧹 مسح الذاكرة المؤقتة...');
            localStorage.clear();
            sessionStorage.clear();
            log('✅ تم مسح الذاكرة المؤقتة');
        }

        // تصدير السجلات
        function exportSystemLogs() {
            const logs = document.getElementById('systemLog').textContent;
            const blob = new Blob([logs], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `system_logs_${new Date().toISOString().slice(0, 10)}.txt`;
            a.click();
            URL.revokeObjectURL(url);
            log('✅ تم تصدير السجلات');
        }

        // فتح التوثيق
        function openDocumentation() {
            window.open('./README_PYTHON_INTEGRATION.md', '_blank');
        }

        // عرض شريط التقدم
        function showProgress(show) {
            document.getElementById('progressSection').style.display = show ? 'block' : 'none';
            if (!show) {
                updateProgress(0, '');
            }
        }

        // تحديث شريط التقدم
        function updateProgress(percent, text) {
            document.getElementById('progressFill').style.width = `${percent}%`;
            document.getElementById('progressText').textContent = text;
        }

        // إضافة رسالة للسجل
        function log(message) {
            const logElement = document.getElementById('systemLog');
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            logElement.textContent += `\n[${timestamp}] ${message}`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        // تهيئة النظام عند تحميل الصفحة
        window.addEventListener('load', initializeSystem);
    </script>
</body>
</html>
