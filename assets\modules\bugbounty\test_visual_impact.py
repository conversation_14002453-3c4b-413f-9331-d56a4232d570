#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار التأثيرات البصرية الحقيقية في الصور
"""

import requests
import json
import time
from pathlib import Path

def test_visual_impact():
    """اختبار التأثيرات البصرية الحقيقية للثغرات"""
    
    print("🔥 اختبار التأثيرات البصرية الحقيقية في الصور")
    print("🎯 لنرى محتوى الصور والفرق بين قبل/أثناء/بعد الاستغلال")
    print("=" * 80)
    
    # اختبار XSS مع تأثيرات بصرية واضحة
    print("\n🧪 اختبار XSS مع تأثيرات بصرية...")
    test_xss_visual_impact()
    
    # اختبار ثغرة جديدة
    print("\n🧪 اختبار Path Traversal مع تأثيرات بصرية...")
    test_path_traversal_visual_impact()
    
    print("\n🎉 تم الانتهاء من اختبار التأثيرات البصرية!")
    print("📊 تحقق من الصور في المجلدات...")

def test_xss_visual_impact():
    """اختبار XSS مع تأثيرات بصرية واضحة"""
    
    report_id = f"visual_xss_{int(time.time())}"
    
    # بيانات XSS
    vuln_data = {
        'url': 'http://testphp.vulnweb.com/search.php',
        'vulnerability_name': 'XSS Cross Site Scripting',
        'vulnerability_type': 'XSS',
        'payload_data': '<script>alert("XSS_VISUAL_TEST")</script>',
        'target_parameter': 'searchFor'
    }
    
    print(f"🎯 اختبار XSS على: {vuln_data['url']}")
    
    # التقاط الصور الثلاث
    stages = ['before', 'during', 'after']
    results = {}
    
    for stage in stages:
        print(f"\n📸 التقاط صورة {stage}...")
        
        data = {
            'url': vuln_data['url'],
            'filename': f'{stage}_XSS_VISUAL',
            'report_id': report_id,
            'vulnerability_name': vuln_data['vulnerability_name'],
            'vulnerability_type': vuln_data['vulnerability_type'],
            'stage': stage,
            'payload_data': vuln_data['payload_data'],
            'target_parameter': vuln_data['target_parameter']
        }
        
        result = capture_screenshot_with_details(data)
        
        if result['success']:
            size = result.get('file_size', 0)
            path = result.get('file_path', 'Unknown')
            results[stage] = {'size': size, 'path': path}
            
            print(f"   ✅ نجح: {size:,} bytes")
            print(f"   📁 المسار: {path}")
        else:
            print(f"   ❌ فشل: {result.get('error', 'خطأ غير محدد')}")
            results[stage] = {'size': 0, 'path': None}
    
    # تحليل الفروق البصرية
    analyze_visual_differences("XSS", results)

def test_path_traversal_visual_impact():
    """اختبار Path Traversal مع تأثيرات بصرية"""
    
    report_id = f"visual_path_{int(time.time())}"
    
    # بيانات Path Traversal
    vuln_data = {
        'url': 'http://testphp.vulnweb.com/search.php',
        'vulnerability_name': 'Path Traversal Attack',
        'vulnerability_type': 'Path_Traversal',
        'payload_data': '../../../etc/passwd',
        'target_parameter': 'file'
    }
    
    print(f"🎯 اختبار Path Traversal على: {vuln_data['url']}")
    
    # التقاط الصور الثلاث
    stages = ['before', 'during', 'after']
    results = {}
    
    for stage in stages:
        print(f"\n📸 التقاط صورة {stage}...")
        
        data = {
            'url': vuln_data['url'],
            'filename': f'{stage}_PATH_VISUAL',
            'report_id': report_id,
            'vulnerability_name': vuln_data['vulnerability_name'],
            'vulnerability_type': vuln_data['vulnerability_type'],
            'stage': stage,
            'payload_data': vuln_data['payload_data'],
            'target_parameter': vuln_data['target_parameter']
        }
        
        result = capture_screenshot_with_details(data)
        
        if result['success']:
            size = result.get('file_size', 0)
            path = result.get('file_path', 'Unknown')
            results[stage] = {'size': size, 'path': path}
            
            print(f"   ✅ نجح: {size:,} bytes")
            print(f"   📁 المسار: {path}")
        else:
            print(f"   ❌ فشل: {result.get('error', 'خطأ غير محدد')}")
            results[stage] = {'size': 0, 'path': None}
    
    # تحليل الفروق البصرية
    analyze_visual_differences("Path Traversal", results)

def capture_screenshot_with_details(data):
    """التقاط صورة مع تفاصيل كاملة"""
    
    try:
        print(f"      🔍 إرسال طلب للسيرفر...")
        print(f"      🎯 المرحلة: {data['stage']}")
        print(f"      💉 Payload: {data.get('payload_data', 'None')}")
        
        response = requests.post(
            'http://localhost:8000/v4_website',
            json=data,
            timeout=45
        )
        
        if response.status_code == 200:
            result = response.json()
            
            if result.get('success'):
                print(f"      ✅ استجابة ناجحة من السيرفر")
            else:
                print(f"      ❌ خطأ من السيرفر: {result.get('error', 'Unknown')}")
                
            return result
        else:
            return {
                'success': False,
                'error': f'HTTP {response.status_code}: {response.text[:100]}'
            }
            
    except Exception as e:
        return {
            'success': False,
            'error': str(e)
        }

def analyze_visual_differences(vuln_type, results):
    """تحليل الفروق البصرية بين الصور"""
    
    print(f"\n📊 تحليل الفروق البصرية لثغرة {vuln_type}:")
    print("=" * 60)
    
    if all(stage in results and results[stage]['size'] > 0 for stage in ['before', 'during', 'after']):
        before_size = results['before']['size']
        during_size = results['during']['size']
        after_size = results['after']['size']
        
        print(f"📸 قبل الاستغلال: {before_size:,} bytes")
        print(f"📸 أثناء الاستغلال: {during_size:,} bytes")
        print(f"📸 بعد الاستغلال: {after_size:,} bytes")
        
        # تحليل التغييرات
        before_during_diff = abs(during_size - before_size)
        during_after_diff = abs(after_size - during_size)
        total_diff = abs(after_size - before_size)
        
        print(f"\n📈 التغييرات في الحجم:")
        print(f"   قبل → أثناء: {before_during_diff:,} bytes")
        print(f"   أثناء → بعد: {during_after_diff:,} bytes")
        print(f"   إجمالي التغيير: {total_diff:,} bytes")
        
        # تقييم التأثيرات البصرية
        print(f"\n🎯 تقييم التأثيرات البصرية:")
        
        if before_during_diff > 1000:
            print(f"   ✅ تأثير واضح في مرحلة 'أثناء الاستغلال'")
        elif before_during_diff > 0:
            print(f"   📊 تأثير طفيف في مرحلة 'أثناء الاستغلال'")
        else:
            print(f"   ❌ لا يوجد تأثير في مرحلة 'أثناء الاستغلال'")
            
        if during_after_diff > 1000:
            print(f"   🎉 تأثير واضح في مرحلة 'بعد الاستغلال'")
        elif during_after_diff > 0:
            print(f"   📊 تأثير طفيف في مرحلة 'بعد الاستغلال'")
        else:
            print(f"   ❌ لا يوجد تأثير في مرحلة 'بعد الاستغلال'")
            
        # توصيات
        print(f"\n💡 التوصيات:")
        if total_diff < 100:
            print(f"   ⚠️ التأثيرات البصرية ضعيفة - يحتاج تحسين payloads")
        elif total_diff < 5000:
            print(f"   📊 التأثيرات البصرية متوسطة - يمكن تحسينها")
        else:
            print(f"   🎉 التأثيرات البصرية واضحة - النظام يعمل بنجاح!")
            
        # عرض مسارات الصور للفحص اليدوي
        print(f"\n📁 مسارات الصور للفحص اليدوي:")
        for stage in ['before', 'during', 'after']:
            if results[stage]['path']:
                print(f"   {stage}: {results[stage]['path']}")
                
    else:
        print(f"❌ لم يتم التقاط جميع الصور بنجاح")

def check_screenshots_folder():
    """فحص مجلد الصور"""
    
    print(f"\n📁 فحص مجلد الصور:")
    
    screenshots_dir = Path("screenshots")
    
    if screenshots_dir.exists():
        visual_files = list(screenshots_dir.rglob("*VISUAL*.png"))
        
        if visual_files:
            print(f"✅ تم العثور على {len(visual_files)} صورة بصرية:")
            
            for file in sorted(visual_files):
                size = file.stat().st_size
                print(f"   📸 {file.name} - {size:,} bytes")
        else:
            print(f"❌ لم يتم العثور على صور بصرية")
    else:
        print(f"❌ مجلد الصور غير موجود")

if __name__ == "__main__":
    test_visual_impact()
    check_screenshots_folder()
