#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json

def test_visual_effects():
    """اختبار التأثيرات البصرية الجديدة"""
    
    print("🔥 اختبار التأثيرات البصرية الجديدة...")
    
    # اختبار XSS مع التأثيرات البصرية الجديدة
    data = {
        'url': 'http://testphp.vulnweb.com/search.php',
        'filename': 'test_visual_xss_fixed',
        'report_id': 'visual_test_fixed',
        'vulnerability_name': 'XSS Cross Site Scripting',
        'vulnerability_type': 'XSS',
        'stage': 'after',
        'payload_data': '<script>alert("XSS_VISUAL_TEST")</script>',
        'target_parameter': 'searchFor'
    }
    
    print("🎯 الثغرة: XSS مع تأثيرات بصرية قوية")
    
    try:
        response = requests.post('http://localhost:8000/v4_website', json=data, timeout=45)
        result = response.json()
        
        if result.get('success'):
            size = result.get('file_size', 0)
            print(f"✅ نجح: {size} bytes")
            
            # فحص إذا كانت التأثيرات البصرية تعمل
            if size > 60000:  # حجم أكبر يعني محتوى إضافي
                print("🎉 التأثيرات البصرية تعمل - حجم أكبر!")
            elif size < 50000:  # حجم أصغر يعني تغيير في المحتوى
                print("🔄 التأثيرات البصرية تعمل - تغيير في المحتوى!")
            else:
                print("❌ التأثيرات البصرية لا تعمل - نفس الحجم")
        else:
            print(f"❌ فشل: {result.get('error', 'Unknown')}")
            
    except Exception as e:
        print(f"❌ خطأ: {e}")

if __name__ == "__main__":
    test_visual_effects()
