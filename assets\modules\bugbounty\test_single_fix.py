#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json

def test_single_vulnerability_fix():
    """اختبار ثغرة واحدة لنرى إذا كان الإصلاح يعمل"""
    
    print("🧪 اختبار النظام الديناميكي المُصلح...")
    
    # اختبار ثغرة Path Traversal الجديدة
    data = {
        'url': 'http://testphp.vulnweb.com/search.php',
        'filename': 'test_path_traversal_fixed',
        'report_id': 'test_dynamic_fix',
        'vulnerability_name': 'Path Traversal Attack',
        'vulnerability_type': 'Path_Traversal',
        'stage': 'after',
        'payload_data': '../../../etc/passwd',
        'target_parameter': 'file'
    }
    
    print("🎯 الثغرة: Path Traversal Attack")
    print("🔍 النوع: Path_Traversal (جديد)")
    
    try:
        response = requests.post('http://localhost:8000/v4_website', json=data, timeout=30)
        result = response.json()
        
        if result.get('success'):
            size = result.get('file_size', 0)
            print(f"✅ نجح: {size} bytes")
            
            # فحص إذا كان النظام الديناميكي يعمل
            if size != 54796:
                print("🎉 النظام الديناميكي يعمل - حجم مختلف!")
                print("✅ الإصلاح نجح!")
            else:
                print("❌ النظام الديناميكي لا يعمل - نفس الحجم")
                print("❌ الإصلاح لم ينجح")
        else:
            print(f"❌ فشل: {result.get('error', 'Unknown')}")
            
    except Exception as e:
        print(f"❌ خطأ: {e}")

if __name__ == "__main__":
    test_single_vulnerability_fix()
