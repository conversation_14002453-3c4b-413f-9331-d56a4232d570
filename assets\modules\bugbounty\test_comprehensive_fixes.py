#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🧪 اختبار شامل وحقيقي للنظام المُصحح
يختبر جميع الإصلاحات: دالة التقاط الصور الثلاث + المحاولات المتعددة + التأثيرات القوية
"""

import requests
import json
import time
import sys
import os

def test_comprehensive_system():
    """اختبار شامل للنظام المُصحح"""
    
    print("🔥" * 80)
    print("🧪 اختبار شامل وحقيقي للنظام المُصحح")
    print("🔥" * 80)
    
    # إعدادات الاختبار
    base_url = "http://localhost:8000"
    test_vulnerabilities = [
        {
            "name": "XSS_Real_Test",
            "url": "http://testphp.vulnweb.com/search.php",
            "payload": "<script>alert('🔥 XSS EXPLOITED! 🔥'); document.body.style.background='red';</script>",
            "parameter": "searchFor"
        },
        {
            "name": "SQL_Injection_Test", 
            "url": "http://testphp.vulnweb.com/artists.php",
            "payload": "1' UNION SELECT 1,2,3,4,5,6,7,8,9,10--",
            "parameter": "artist"
        },
        {
            "name": "Command_Injection_Test",
            "url": "http://testphp.vulnweb.com/admin",
            "payload": "; whoami; id; uname -a",
            "parameter": "cmd"
        }
    ]
    
    print(f"🎯 عدد الثغرات للاختبار: {len(test_vulnerabilities)}")
    print(f"🌐 السيرفر: {base_url}")
    print()
    
    # اختبار كل ثغرة
    total_tests = 0
    successful_tests = 0
    
    for i, vuln in enumerate(test_vulnerabilities, 1):
        print(f"\n{'='*60}")
        print(f"🔍 اختبار {i}/{len(test_vulnerabilities)}: {vuln['name']}")
        print(f"{'='*60}")
        
        result = test_single_vulnerability(base_url, vuln, i)
        total_tests += 1
        
        if result:
            successful_tests += 1
            print(f"✅ نجح اختبار {vuln['name']}")
        else:
            print(f"❌ فشل اختبار {vuln['name']}")
    
    # النتائج النهائية
    print(f"\n{'🎉' * 60}")
    print(f"📊 النتائج النهائية للاختبار الشامل:")
    print(f"   ✅ نجح: {successful_tests}/{total_tests}")
    print(f"   ❌ فشل: {total_tests - successful_tests}/{total_tests}")
    print(f"   📈 معدل النجاح: {(successful_tests/total_tests)*100:.1f}%")
    print(f"{'🎉' * 60}")
    
    return successful_tests == total_tests

def test_single_vulnerability(base_url, vuln, test_number):
    """اختبار ثغرة واحدة مع التقاط الصور الثلاث"""
    
    print(f"🎯 الثغرة: {vuln['name']}")
    print(f"🔗 الرابط: {vuln['url']}")
    print(f"💉 Payload: {vuln['payload']}")
    print(f"📝 المعامل: {vuln['parameter']}")
    
    # إعداد البيانات للطلب
    report_id = f"comprehensive_test_{test_number}_{int(time.time())}"
    
    request_data = {
        "url": vuln['url'],
        "filename": f"test_{vuln['name']}",
        "report_id": report_id,
        "vulnerability_name": vuln['name'],
        "vulnerability_type": vuln['name'].split('_')[0],  # XSS, SQL, Command
        "payload_data": vuln['payload'],
        "target_parameter": vuln['parameter'],
        "stage": "comprehensive_test"  # اختبار شامل
    }
    
    print(f"📡 إرسال طلب للسيرفر...")
    print(f"📋 معرف التقرير: {report_id}")
    
    try:
        # إرسال الطلب
        response = requests.post(
            f"{base_url}/v4_website",
            json=request_data,
            headers={"Content-Type": "application/json"},
            timeout=120  # انتظار أطول للاختبار الشامل
        )
        
        print(f"📊 كود الاستجابة: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            return analyze_test_result(result, vuln['name'])
        else:
            print(f"❌ خطأ HTTP: {response.status_code}")
            print(f"📄 محتوى الخطأ: {response.text[:200]}...")
            return False
            
    except requests.exceptions.Timeout:
        print(f"⏰ انتهت مهلة الانتظار للاختبار")
        return False
    except Exception as e:
        print(f"❌ خطأ في الطلب: {e}")
        return False

def analyze_test_result(result, vuln_name):
    """تحليل نتيجة الاختبار"""
    
    print(f"\n📋 تحليل نتائج {vuln_name}:")
    
    if not result.get('success'):
        print(f"❌ الاختبار فشل: {result.get('error', 'خطأ غير محدد')}")
        return False
    
    # فحص الصور الثلاث
    stages = ['before', 'during', 'after']
    successful_stages = 0
    
    for stage in stages:
        if stage in result and result[stage]:
            stage_result = result[stage]
            if isinstance(stage_result, dict) and stage_result.get('success'):
                print(f"   ✅ صورة {stage}: نجحت")
                successful_stages += 1
            else:
                print(f"   ❌ صورة {stage}: فشلت")
        else:
            print(f"   ❌ صورة {stage}: غير موجودة")
    
    # فحص التفاصيل الإضافية
    if 'sequence_success' in result:
        print(f"   🎯 نجاح التسلسل: {result['sequence_success']}")
    
    if 'total_screenshots' in result:
        print(f"   📸 عدد الصور: {result['total_screenshots']}")
    
    # تحديد النجاح
    success = successful_stages >= 2  # على الأقل صورتان من الثلاث
    
    if success:
        print(f"   🎉 نجح الاختبار: {successful_stages}/3 صور")
    else:
        print(f"   ⚠️ نجح جزئياً: {successful_stages}/3 صور")
    
    return success

if __name__ == "__main__":
    print("🚀 بدء الاختبار الشامل...")
    
    # انتظار تشغيل السيرفر
    print("⏳ انتظار تشغيل السيرفر...")
    time.sleep(5)
    
    # تشغيل الاختبار
    success = test_comprehensive_system()
    
    if success:
        print("\n🎉 جميع الاختبارات نجحت! النظام جاهز للفحص الرئيسي.")
        sys.exit(0)
    else:
        print("\n⚠️ بعض الاختبارات فشلت. يحتاج النظام لمزيد من الإصلاحات.")
        sys.exit(1)
