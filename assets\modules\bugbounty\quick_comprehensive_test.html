<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار سريع للدالة الشاملة v4.0</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f0f8ff; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; }
        .result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button { background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; margin: 5px; }
        button:hover { background: #0056b3; }
        .code { background: #f8f9fa; border: 1px solid #dee2e6; padding: 10px; border-radius: 5px; font-family: monospace; white-space: pre-wrap; max-height: 300px; overflow-y: auto; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 اختبار سريع للدالة الشاملة التفصيلية v4.0</h1>
        
        <button onclick="runQuickTest()">🚀 تشغيل الاختبار السريع</button>
        <button onclick="clearResults()">🗑️ مسح النتائج</button>
        
        <div id="results"></div>
        <div id="details" class="code"></div>
    </div>

    <script src="BugBountyCore.js"></script>
    <script>
        function addResult(message, type = 'info') {
            const div = document.createElement('div');
            div.className = `result ${type}`;
            div.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            document.getElementById('results').appendChild(div);
            
            const details = document.getElementById('details');
            details.textContent += `${message}\n`;
            details.scrollTop = details.scrollHeight;
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
            document.getElementById('details').textContent = '';
        }

        async function runQuickTest() {
            clearResults();
            addResult('🚀 بدء الاختبار السريع...', 'info');

            try {
                // تهيئة النظام
                const bugBountyCore = new BugBountyCore();
                addResult('✅ تم تهيئة BugBountyCore', 'success');

                // التحقق من وجود الدالة
                if (typeof bugBountyCore.generateComprehensiveDetailsFromRealData === 'function') {
                    addResult('✅ الدالة generateComprehensiveDetailsFromRealData موجودة', 'success');
                } else {
                    addResult('❌ الدالة generateComprehensiveDetailsFromRealData غير موجودة', 'error');
                    return;
                }

                // إنشاء ثغرة اختبار
                const testVuln = {
                    name: 'XSS Reflected Test',
                    type: 'XSS',
                    severity: 'High',
                    url: 'http://testphp.vulnweb.com/search.php',
                    location: 'search parameter',
                    payload: '<script>alert("XSS")</script>',
                    response: 'Script executed successfully',
                    evidence: 'Alert dialog appeared'
                };

                const realData = {
                    payload: testVuln.payload,
                    response: testVuln.response,
                    evidence: testVuln.evidence,
                    url: testVuln.url,
                    injection_point: testVuln.location,
                    exploitation_result: 'XSS payload executed successfully'
                };

                addResult('📊 تم إنشاء بيانات الاختبار', 'info');

                // تنفيذ الدالة
                addResult('⚡ تنفيذ الدالة الشاملة...', 'info');
                const result = await bugBountyCore.generateComprehensiveDetailsFromRealData(testVuln, realData);

                if (result) {
                    addResult('✅ تم تنفيذ الدالة بنجاح', 'success');
                    
                    // التحقق من الخصائص الأساسية
                    const properties = Object.keys(result);
                    addResult(`📋 الكائن المُرجع يحتوي على ${properties.length} خاصية`, 'info');
                    
                    // التحقق من الخصائص المطلوبة
                    const requiredProps = [
                        'technical_details', 'impact_analysis', 'exploitation_results',
                        'interactive_dialogue', 'evidence', 'recommendations'
                    ];
                    
                    let foundProps = 0;
                    requiredProps.forEach(prop => {
                        if (result[prop]) {
                            foundProps++;
                            addResult(`✅ ${prop} موجود`, 'success');
                        } else {
                            addResult(`❌ ${prop} مفقود`, 'error');
                        }
                    });

                    // التحقق من البيانات الحقيقية
                    if (result.technical_details && result.technical_details.real_payload_used === testVuln.payload) {
                        addResult('✅ تم استخدام البيانات الحقيقية', 'success');
                    } else {
                        addResult('⚠️ قد لا تكون البيانات الحقيقية مستخدمة', 'error');
                    }

                    // التحقق من عدم وجود محتوى افتراضي
                    const resultStr = JSON.stringify(result);
                    const genericContent = ['payload متخصص', '[object Object]', 'تحت التقييم', 'غير محدد'];
                    let hasGeneric = false;
                    
                    genericContent.forEach(content => {
                        if (resultStr.includes(content)) {
                            hasGeneric = true;
                            addResult(`⚠️ تم العثور على محتوى عام: ${content}`, 'error');
                        }
                    });

                    if (!hasGeneric) {
                        addResult('✅ لا يوجد محتوى عام أو افتراضي', 'success');
                    }

                    // النتيجة النهائية
                    const successRate = (foundProps / requiredProps.length) * 100;
                    if (successRate === 100 && !hasGeneric) {
                        addResult('🎉 الاختبار نجح بنسبة 100%! الدالة تعمل بشكل مثالي', 'success');
                    } else if (successRate >= 80) {
                        addResult(`✅ الاختبار نجح بنسبة ${successRate.toFixed(1)}% - جيد جداً`, 'success');
                    } else {
                        addResult(`⚠️ الاختبار نجح بنسبة ${successRate.toFixed(1)}% - يحتاج تحسين`, 'error');
                    }

                    // عرض عينة من النتائج
                    addResult('📄 عينة من النتائج:', 'info');
                    if (result.technical_details) {
                        addResult(`التفاصيل التقنية: ${JSON.stringify(result.technical_details).substring(0, 100)}...`, 'info');
                    }
                    if (result.exploitation_results) {
                        addResult(`نتائج الاستغلال: ${JSON.stringify(result.exploitation_results).substring(0, 100)}...`, 'info');
                    }

                } else {
                    addResult('❌ الدالة لم ترجع أي نتائج', 'error');
                }

            } catch (error) {
                addResult(`❌ خطأ في الاختبار: ${error.message}`, 'error');
                console.error('تفاصيل الخطأ:', error);
            }

            addResult('🏁 انتهى الاختبار السريع', 'info');
        }

        // تشغيل تلقائي عند تحميل الصفحة
        window.onload = function() {
            addResult('📋 تم تحميل صفحة الاختبار السريع', 'info');
            addResult('🎯 اضغط على "تشغيل الاختبار السريع" للبدء', 'info');
        };
    </script>
</body>
</html>
