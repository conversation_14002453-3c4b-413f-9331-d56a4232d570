#!/usr/bin/env python3
"""
اختبار بسيط لتشخيص المشكلة
"""

import requests
import json

def test_simple():
    """اختبار بسيط"""
    try:
        payload = {
            "url": "https://httpbin.org/html",
            "vulnerability_name": "XSS_Test",
            "vulnerability_type": "Cross-Site Scripting",
            "stage": "before",
            "payload_data": "<script>alert('test');</script>",
            "target_parameter": "test_param",
            "filename": "test_simple",
            "report_id": "test_simple_123"
        }
        
        print("📤 إرسال طلب اختبار بسيط...")
        print(f"📋 البيانات: {json.dumps(payload, indent=2, ensure_ascii=False)}")
        
        response = requests.post(
            "http://localhost:8000/v4_website",
            json=payload,
            timeout=10,  # timeout أقصر للاختبار
            headers={'Content-Type': 'application/json'}
        )
        
        print(f"📥 رد السيرفر: {response.status_code}")
        print(f"📄 Response: {response.text}")
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")

if __name__ == "__main__":
    test_simple()
