// اختبار النظام v4 الفعلي لإنشاء تقرير حقيقي

// محاكاة استيراد النظام v4
const fs = require('fs');
const path = require('path');

// محاكاة النظام v4
class BugBountyV4Test {
    constructor() {
        this.vulnerabilities = [
            {
                name: 'XSS Cross Site Scripting',
                type: 'XSS',
                severity: 'High',
                description: 'Cross-Site Scripting vulnerability found in search parameter',
                impact: 'Allows execution of malicious JavaScript code',
                recommendation: 'Implement proper input validation and output encoding',
                screenshots: {
                    before: './assets/modules/bugbounty/screenshots/real_report_test_1753369845/single_before_XSS_REPORT_TEST_1.png',
                    during: './assets/modules/bugbounty/screenshots/real_report_test_1753369845/single_during_XSS_REPORT_TEST_1.png',
                    after: './assets/modules/bugbounty/screenshots/real_report_test_1753369845/single_after_XSS_REPORT_TEST_1.png'
                },
                visual_proof: {
                    before_screenshot_path: './assets/modules/bugbounty/screenshots/real_report_test_1753369845/single_before_XSS_REPORT_TEST_1.png',
                    during_screenshot_path: './assets/modules/bugbounty/screenshots/real_report_test_1753369845/single_during_XSS_REPORT_TEST_1.png',
                    after_screenshot_path: './assets/modules/bugbounty/screenshots/real_report_test_1753369845/single_after_XSS_REPORT_TEST_1.png'
                }
            },
            {
                name: 'SQL Injection Attack',
                type: 'SQL_Injection',
                severity: 'Critical',
                description: 'SQL Injection vulnerability found in id parameter',
                impact: 'Allows unauthorized access to database information',
                recommendation: 'Use parameterized queries and input validation',
                screenshots: {
                    before: './assets/modules/bugbounty/screenshots/real_report_test_1753369845/single_before_SQL_Injection_REPORT_TEST_2.png',
                    during: './assets/modules/bugbounty/screenshots/real_report_test_1753369845/single_during_SQL_Injection_REPORT_TEST_2.png',
                    after: './assets/modules/bugbounty/screenshots/real_report_test_1753369845/single_after_SQL_Injection_REPORT_TEST_2.png'
                },
                visual_proof: {
                    before_screenshot_path: './assets/modules/bugbounty/screenshots/real_report_test_1753369845/single_before_SQL_Injection_REPORT_TEST_2.png',
                    during_screenshot_path: './assets/modules/bugbounty/screenshots/real_report_test_1753369845/single_during_SQL_Injection_REPORT_TEST_2.png',
                    after_screenshot_path: './assets/modules/bugbounty/screenshots/real_report_test_1753369845/single_after_SQL_Injection_REPORT_TEST_2.png'
                }
            }
        ];
    }

    // محاكاة دالة البحث عن الصور من النظام v4
    findRealImageForVulnerability(vuln, stage) {
        console.log(`🔍 البحث عن مسار صورة ${stage} للثغرة: ${vuln.name}`);

        // البحث في مسارات الصور المحفوظة أولاً
        if (vuln.screenshots && vuln.screenshots[stage]) {
            const savedPath = vuln.screenshots[stage];
            if (typeof savedPath === 'string' && savedPath.includes('./assets/modules/bugbounty/screenshots/')) {
                console.log(`✅ تم العثور على مسار صورة ${stage} محفوظ: ${savedPath}`);
                return savedPath;
            }
        }

        // البحث في visual_proof paths
        if (vuln.visual_proof && vuln.visual_proof[`${stage}_screenshot_path`]) {
            const savedPath = vuln.visual_proof[`${stage}_screenshot_path`];
            console.log(`✅ تم العثور على مسار صورة ${stage} في visual_proof: ${savedPath}`);
            return savedPath;
        }

        console.log(`❌ لم يتم العثور على صورة للمرحلة ${stage}`);
        return null;
    }

    // إنشاء HTML للصور
    generateImageHTML(vuln, stage) {
        const imagePath = this.findRealImageForVulnerability(vuln, stage);
        
        if (imagePath) {
            const stageText = {
                'before': 'قبل الاستغلال',
                'during': 'أثناء الاستغلال',
                'after': 'بعد الاستغلال'
            };

            return `
                <div class="image-container" style="margin: 15px 0; padding: 10px; border: 1px solid #ddd; border-radius: 5px;">
                    <h4>${stageText[stage]}:</h4>
                    <p style="font-size: 12px; color: #666;">المسار: <code>${imagePath}</code></p>
                    <img src="${imagePath}" alt="${stageText[stage]}" 
                         style="max-width: 400px; max-height: 300px; border: 2px solid #007bff; border-radius: 5px;"
                         onload="console.log('✅ تم تحميل صورة ${stage} بنجاح')"
                         onerror="console.log('❌ فشل في تحميل صورة ${stage}: ${imagePath}'); this.style.display='none'; this.nextElementSibling.style.display='block';">
                    <div style="display:none; color: red; padding: 10px; background: #f8d7da; border-radius: 5px;">
                        ❌ فشل في تحميل الصورة: ${imagePath}
                    </div>
                </div>
            `;
        }

        return `
            <div class="image-container" style="margin: 15px 0; padding: 10px; border: 1px solid #ddd; border-radius: 5px;">
                <h4>${stage}:</h4>
                <div style="color: orange; padding: 10px; background: #fff3cd; border-radius: 5px;">
                    ⚠️ لم يتم العثور على صورة للمرحلة ${stage}
                </div>
            </div>
        `;
    }

    // إنشاء التقرير الرئيسي
    generateMainReport() {
        console.log('📋 إنشاء التقرير الرئيسي...');

        let html = `
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>التقرير الرئيسي v4 - اختبار الصور</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; }
        .vulnerability { margin: 30px 0; padding: 20px; border: 1px solid #ddd; border-radius: 8px; }
        .section { margin: 20px 0; padding: 15px; background: #f8f9fa; border-radius: 5px; }
        .visual-changes { background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%); }
        h1 { color: #007bff; text-align: center; }
        h2 { color: #28a745; border-bottom: 2px solid #28a745; padding-bottom: 10px; }
        h3 { color: #17a2b8; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔥 التقرير الرئيسي v4 - اختبار عرض الصور</h1>
        <p><strong>تاريخ الإنشاء:</strong> ${new Date().toLocaleString('ar-SA')}</p>
        <p><strong>عدد الثغرات:</strong> ${this.vulnerabilities.length}</p>
        
        <div class="section">
            <h2>📊 ملخص الثغرات</h2>
            <p>تم اكتشاف ${this.vulnerabilities.length} ثغرة أمنية مع صور توضيحية كاملة.</p>
        </div>
`;

        this.vulnerabilities.forEach((vuln, index) => {
            html += `
        <div class="vulnerability">
            <h2>🎯 الثغرة ${index + 1}: ${vuln.name}</h2>
            <p><strong>النوع:</strong> ${vuln.type}</p>
            <p><strong>الخطورة:</strong> ${vuln.severity}</p>
            <p><strong>الوصف:</strong> ${vuln.description}</p>
            <p><strong>التأثير:</strong> ${vuln.impact}</p>
            
            <div class="section visual-changes">
                <h3>📸 الصور التوضيحية والتأثيرات البصرية</h3>
                <p>الصور التالية تُظهر الثغرة في مراحل مختلفة من الاستغلال:</p>
                
                ${this.generateImageHTML(vuln, 'before')}
                ${this.generateImageHTML(vuln, 'during')}
                ${this.generateImageHTML(vuln, 'after')}
            </div>
            
            <div class="section">
                <h3>💡 التوصيات</h3>
                <p>${vuln.recommendation}</p>
            </div>
        </div>
`;
        });

        html += `
    </div>
    
    <script>
        console.log('🔥 تم تحميل التقرير الرئيسي v4');
        
        // إحصائيات الصور
        let totalImages = 0;
        let loadedImages = 0;
        let failedImages = 0;
        
        document.querySelectorAll('img').forEach(img => {
            totalImages++;
            
            img.addEventListener('load', function() {
                loadedImages++;
                console.log(\`✅ تم تحميل صورة (\${loadedImages}/\${totalImages})\`);
                updateStats();
            });
            
            img.addEventListener('error', function() {
                failedImages++;
                console.log(\`❌ فشل في تحميل صورة (\${failedImages}/\${totalImages})\`);
                updateStats();
            });
        });
        
        function updateStats() {
            if (loadedImages + failedImages === totalImages) {
                console.log(\`📊 إحصائيات نهائية: \${loadedImages} نجحت، \${failedImages} فشلت من أصل \${totalImages}\`);
            }
        }
        
        console.log(\`📸 إجمالي الصور في التقرير: \${totalImages}\`);
    </script>
</body>
</html>
`;

        return html;
    }

    // إنشاء التقرير المنفصل
    generateSeparateReport() {
        console.log('📄 إنشاء التقرير المنفصل...');

        let html = `
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>التقرير المنفصل v4 - اختبار الصور</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f8f9fa; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; }
        .vulnerability { margin: 30px 0; padding: 25px; border: 2px solid #17a2b8; border-radius: 10px; }
        .section { margin: 20px 0; padding: 20px; background: #e9ecef; border-radius: 8px; }
        .visual-changes { background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%); border: 2px solid #17a2b8; }
        h1 { color: #17a2b8; text-align: center; }
        h2 { color: #495057; }
        h3 { color: #17a2b8; }
    </style>
</head>
<body>
    <div class="container">
        <h1>📄 التقرير المنفصل v4 - تفاصيل الثغرات مع الصور</h1>
        <p><strong>تقرير مفصل يحتوي على جميع الأدلة البصرية للثغرات المكتشفة</strong></p>
`;

        this.vulnerabilities.forEach((vuln, index) => {
            html += `
        <div class="vulnerability">
            <h2>📋 تفاصيل الثغرة ${index + 1}: ${vuln.name}</h2>
            
            <div class="section">
                <h3>ℹ️ معلومات الثغرة</h3>
                <p><strong>النوع:</strong> ${vuln.type}</p>
                <p><strong>مستوى الخطورة:</strong> ${vuln.severity}</p>
                <p><strong>الوصف التفصيلي:</strong> ${vuln.description}</p>
            </div>
            
            <div class="section visual-changes">
                <h3>🖼️ الأدلة البصرية (Visual Proof)</h3>
                <p>الصور التالية تُظهر الدليل البصري على وجود الثغرة وتأثيرها:</p>
                
                ${this.generateImageHTML(vuln, 'before')}
                ${this.generateImageHTML(vuln, 'during')}
                ${this.generateImageHTML(vuln, 'after')}
            </div>
            
            <div class="section">
                <h3>⚠️ تحليل التأثير</h3>
                <p>${vuln.impact}</p>
            </div>
            
            <div class="section">
                <h3>🔧 خطوات الإصلاح</h3>
                <p>${vuln.recommendation}</p>
            </div>
        </div>
`;
        });

        html += `
    </div>
    
    <script>
        console.log('📄 تم تحميل التقرير المنفصل v4');
        
        // إحصائيات مفصلة للصور
        let imageStats = {
            total: 0,
            loaded: 0,
            failed: 0,
            byStage: { before: 0, during: 0, after: 0 }
        };
        
        document.querySelectorAll('img').forEach(img => {
            imageStats.total++;
            
            // تحديد المرحلة من alt text
            const stage = img.alt.includes('قبل') ? 'before' : 
                         img.alt.includes('أثناء') ? 'during' : 'after';
            
            img.addEventListener('load', function() {
                imageStats.loaded++;
                imageStats.byStage[stage]++;
                console.log(\`✅ تم تحميل صورة \${stage} (\${imageStats.loaded}/\${imageStats.total})\`);
                updateDetailedStats();
            });
            
            img.addEventListener('error', function() {
                imageStats.failed++;
                console.log(\`❌ فشل في تحميل صورة \${stage} (\${imageStats.failed}/\${imageStats.total})\`);
                updateDetailedStats();
            });
        });
        
        function updateDetailedStats() {
            if (imageStats.loaded + imageStats.failed === imageStats.total) {
                console.log('📊 إحصائيات مفصلة للتقرير المنفصل:');
                console.log(\`   إجمالي: \${imageStats.total}\`);
                console.log(\`   نجحت: \${imageStats.loaded}\`);
                console.log(\`   فشلت: \${imageStats.failed}\`);
                console.log(\`   قبل الاستغلال: \${imageStats.byStage.before}\`);
                console.log(\`   أثناء الاستغلال: \${imageStats.byStage.during}\`);
                console.log(\`   بعد الاستغلال: \${imageStats.byStage.after}\`);
            }
        }
        
        console.log(\`📸 إجمالي الصور في التقرير المنفصل: \${imageStats.total}\`);
    </script>
</body>
</html>
`;

        return html;
    }

    // تشغيل الاختبار الكامل
    runFullTest() {
        console.log('🚀 بدء اختبار النظام v4 الكامل...');
        console.log('=' * 60);

        // اختبار البحث عن الصور
        console.log('\n🔍 اختبار البحث عن الصور:');
        this.vulnerabilities.forEach((vuln, index) => {
            console.log(`\n🎯 الثغرة ${index + 1}: ${vuln.name}`);
            
            ['before', 'during', 'after'].forEach(stage => {
                const imagePath = this.findRealImageForVulnerability(vuln, stage);
                if (imagePath) {
                    console.log(`   ✅ ${stage}: ${imagePath}`);
                } else {
                    console.log(`   ❌ ${stage}: غير موجود`);
                }
            });
        });

        // إنشاء التقارير
        console.log('\n📋 إنشاء التقارير...');
        
        const mainReport = this.generateMainReport();
        const separateReport = this.generateSeparateReport();

        // حفظ التقارير
        const timestamp = Date.now();
        const mainReportPath = `v4_main_report_${timestamp}.html`;
        const separateReportPath = `v4_separate_report_${timestamp}.html`;

        fs.writeFileSync(mainReportPath, mainReport, 'utf8');
        fs.writeFileSync(separateReportPath, separateReport, 'utf8');

        console.log(`✅ تم حفظ التقرير الرئيسي: ${mainReportPath}`);
        console.log(`✅ تم حفظ التقرير المنفصل: ${separateReportPath}`);

        console.log('\n🎉 انتهى الاختبار بنجاح!');
        console.log('📖 افتح الملفات في المتصفح لرؤية النتائج');

        return {
            mainReportPath,
            separateReportPath,
            vulnerabilitiesCount: this.vulnerabilities.length,
            totalImages: this.vulnerabilities.length * 3 * 2 // 3 مراحل × 2 مسار لكل ثغرة
        };
    }
}

// تشغيل الاختبار
const tester = new BugBountyV4Test();
const results = tester.runFullTest();

console.log('\n📊 ملخص النتائج:');
console.log(`   الثغرات: ${results.vulnerabilitiesCount}`);
console.log(`   الصور المتوقعة: ${results.totalImages}`);
console.log(`   التقرير الرئيسي: ${results.mainReportPath}`);
console.log(`   التقرير المنفصل: ${results.separateReportPath}`);
