#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import time
from pathlib import Path

def convert_to_relative_path(absolute_path):
    """تحويل المسار المطلق إلى مسار نسبي للعرض في التقارير"""
    try:
        # إزالة المسار الأساسي وتحويل إلى مسار نسبي
        if 'assets\\modules\\bugbounty\\' in absolute_path:
            relative_path = absolute_path.split('assets\\modules\\bugbounty\\')[1]
            # تحويل backslashes إلى forward slashes للويب
            relative_path = relative_path.replace('\\', '/')
            return relative_path
        elif 'assets/modules/bugbounty/' in absolute_path:
            relative_path = absolute_path.split('assets/modules/bugbounty/')[1]
            return relative_path
        else:
            # إذا لم يكن المسار يحتوي على المجلد المتوقع، استخدم اسم الملف فقط
            return Path(absolute_path).name
    except Exception as e:
        print(f"⚠️ خطأ في تحويل المسار: {e}")
        return Path(absolute_path).name

def create_final_reports_with_existing_images():
    """إنشاء تقارير نهائية مع الصور الموجودة فعلياً"""
    
    print("🔥 إنشاء تقارير نهائية مع الصور الموجودة فعلياً")
    print("=" * 70)
    
    # بيانات الثغرات مع المسارات الصحيحة للصور الموجودة
    report_id = "simple_real_1753390267359"  # استخدام المجلد الأحدث
    base_path = f"E:\\المساعد ai\\مساعد 2\\assets\\modules\\bugbounty\\screenshots\\{report_id}"
    
    vulnerabilities = [
        {
            'name': 'XSS Cross Site Scripting',
            'type': 'XSS',
            'severity': 'High',
            'description': 'Cross-Site Scripting vulnerability with real visual impact',
            'impact': 'Allows execution of malicious JavaScript code with visual proof',
            'recommendation': 'Implement proper input validation and output encoding',
            'screenshots': {
                'before': f"{base_path}\\before_before_XSS_SIMPLE_TEST_1.png",
                'during': f"{base_path}\\during_during_XSS_SIMPLE_TEST_1.png",
                'after': f"{base_path}\\after_after_XSS_SIMPLE_TEST_1.png"
            },
            'visual_proof': {
                'before_screenshot_path': f"{base_path}\\before_before_XSS_SIMPLE_TEST_1.png",
                'during_screenshot_path': f"{base_path}\\during_during_XSS_SIMPLE_TEST_1.png",
                'after_screenshot_path': f"{base_path}\\after_after_XSS_SIMPLE_TEST_1.png"
            }
        },
        {
            'name': 'SQL Injection Attack',
            'type': 'SQL_Injection',
            'severity': 'Critical',
            'description': 'SQL Injection vulnerability with database information disclosure',
            'impact': 'Allows unauthorized access to database information with visual evidence',
            'recommendation': 'Use parameterized queries and input validation',
            'screenshots': {
                'before': f"{base_path}\\single_before_SQL_Injection_FINAL_TEST_2.png",
                'during': f"{base_path}\\single_during_SQL_Injection_FINAL_TEST_2.png",
                'after': f"{base_path}\\single_after_SQL_Injection_FINAL_TEST_2.png"
            },
            'visual_proof': {
                'before_screenshot_path': f"{base_path}\\single_before_SQL_Injection_FINAL_TEST_2.png",
                'during_screenshot_path': f"{base_path}\\single_during_SQL_Injection_FINAL_TEST_2.png",
                'after_screenshot_path': f"{base_path}\\single_after_SQL_Injection_FINAL_TEST_2.png"
            }
        }
    ]
    
    # التحقق من وجود الصور
    print("\n🔍 التحقق من وجود الصور:")
    all_images_exist = True
    
    for vuln in vulnerabilities:
        print(f"\n🎯 {vuln['name']}:")
        for stage, path in vuln['screenshots'].items():
            if os.path.exists(path):
                size = os.path.getsize(path)
                print(f"   ✅ {stage}: {size:,} bytes")
            else:
                print(f"   ❌ {stage}: غير موجود")
                all_images_exist = False
    
    if not all_images_exist:
        print("\n❌ بعض الصور غير موجودة!")
        return
    
    # إنشاء التقارير
    print(f"\n📋 إنشاء التقارير النهائية...")
    
    # التقرير الرئيسي
    main_report_html = generate_final_main_report(vulnerabilities, report_id)
    main_report_path = f"FINAL_main_report_{report_id}.html"
    
    with open(main_report_path, 'w', encoding='utf-8') as f:
        f.write(main_report_html)
    
    print(f"✅ تم إنشاء التقرير الرئيسي النهائي: {main_report_path}")
    
    # التقرير المنفصل
    separate_report_html = generate_final_separate_report(vulnerabilities, report_id)
    separate_report_path = f"FINAL_separate_report_{report_id}.html"
    
    with open(separate_report_path, 'w', encoding='utf-8') as f:
        f.write(separate_report_html)
    
    print(f"✅ تم إنشاء التقرير المنفصل النهائي: {separate_report_path}")
    
    # عرض النتائج النهائية
    print(f"\n🎉 تم إنشاء التقارير النهائية بنجاح!")
    print(f"📖 افتح التقارير في المتصفح:")
    print(f"   📋 التقرير الرئيسي: file:///{os.path.abspath(main_report_path)}")
    print(f"   📄 التقرير المنفصل: file:///{os.path.abspath(separate_report_path)}")
    
    return main_report_path, separate_report_path

def generate_final_main_report(vulnerabilities, report_id):
    """إنشاء التقرير الرئيسي النهائي"""
    
    html = f"""
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>🔥 التقرير الرئيسي النهائي v4 - مع الصور الحقيقية</title>
    <style>
        body {{ 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
            margin: 0; 
            padding: 20px; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }}
        .container {{ 
            max-width: 1200px; 
            margin: 0 auto; 
            background: white; 
            padding: 40px; 
            border-radius: 15px; 
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }}
        .header {{
            text-align: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 30px;
        }}
        .vulnerability {{ 
            margin: 40px 0; 
            padding: 30px; 
            border: 2px solid #007bff; 
            border-radius: 15px; 
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }}
        .section {{ 
            margin: 25px 0; 
            padding: 20px; 
            background: #f8f9fa; 
            border-radius: 10px; 
            border-left: 5px solid #007bff;
        }}
        .visual-changes {{ 
            background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%); 
            border: 3px solid #17a2b8; 
            border-radius: 15px;
            padding: 25px;
        }}
        .image-container {{ 
            margin: 20px 0; 
            padding: 20px; 
            border: 2px solid #ddd; 
            border-radius: 10px; 
            background: white;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }}
        .image-container img {{ 
            max-width: 100%; 
            height: auto; 
            border: 3px solid #007bff; 
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }}
        .success {{ 
            color: #155724; 
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%); 
            padding: 15px; 
            border-radius: 8px; 
            margin: 15px 0; 
            border: 2px solid #28a745;
            font-weight: bold;
        }}
        .error {{ 
            color: #721c24; 
            background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%); 
            padding: 15px; 
            border-radius: 8px; 
            margin: 15px 0; 
            border: 2px solid #dc3545;
            font-weight: bold;
        }}
        .path-info {{ 
            font-family: 'Courier New', monospace; 
            background: #e9ecef; 
            padding: 10px; 
            border-radius: 5px; 
            font-size: 11px; 
            color: #495057;
            border: 1px solid #ced4da;
        }}
        h1 {{ color: white; font-size: 2.5em; margin: 0; }}
        h2 {{ color: #007bff; border-bottom: 3px solid #007bff; padding-bottom: 15px; font-size: 1.8em; }}
        h3 {{ color: #17a2b8; font-size: 1.4em; }}
        h4 {{ color: #28a745; font-size: 1.2em; }}
        .stats {{
            background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            text-align: center;
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔥 التقرير الرئيسي النهائي v4</h1>
            <p style="font-size: 1.2em; margin: 10px 0;">تقرير شامل مع الصور الحقيقية والتأثيرات البصرية</p>
            <p><strong>تاريخ الإنشاء:</strong> {time.strftime('%Y-%m-%d %H:%M:%S')}</p>
            <p><strong>Report ID:</strong> {report_id}</p>
        </div>
        
        <div class="stats">
            <h2 style="color: white; border: none; margin: 0;">📊 ملخص الثغرات</h2>
            <p style="font-size: 1.1em;">تم اكتشاف {len(vulnerabilities)} ثغرة أمنية مع صور توضيحية كاملة وتأثيرات بصرية حقيقية</p>
            <p><strong>إجمالي الصور:</strong> {len(vulnerabilities) * 3} صورة</p>
        </div>
"""

    for i, vuln in enumerate(vulnerabilities, 1):
        html += f"""
        <div class="vulnerability">
            <h2>🎯 الثغرة {i}: {vuln['name']}</h2>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 20px 0;">
                <div>
                    <p><strong>النوع:</strong> <span style="color: #007bff;">{vuln['type']}</span></p>
                    <p><strong>الخطورة:</strong> <span style="color: {'#dc3545' if vuln['severity'] == 'Critical' else '#fd7e14'};">{vuln['severity']}</span></p>
                </div>
                <div>
                    <p><strong>الوصف:</strong> {vuln['description']}</p>
                    <p><strong>التأثير:</strong> {vuln['impact']}</p>
                </div>
            </div>
            
            <div class="section visual-changes">
                <h3>📸 الصور التوضيحية والتأثيرات البصرية</h3>
                <p style="font-size: 1.1em; font-weight: bold;">الصور التالية تُظهر الثغرة في مراحل مختلفة من الاستغلال مع التأثيرات الحقيقية:</p>
"""
        
        # إضافة الصور مع مسارات صحيحة
        stages = ['before', 'during', 'after']
        stage_names = {
            'before': '📊 قبل الاستغلال - الحالة الأصلية', 
            'during': '⚡ أثناء الاستغلال - تطبيق الـ Payload', 
            'after': '🔥 بعد الاستغلال - التأثيرات الحقيقية'
        }
        stage_descriptions = {
            'before': 'الموقع في حالته الطبيعية قبل تطبيق أي استغلال',
            'during': 'الموقع أثناء تطبيق payload الثغرة',
            'after': 'الموقع بعد الاستغلال مع إظهار التأثيرات الحقيقية للثغرة'
        }
        
        for stage in stages:
            if stage in vuln['screenshots']:
                image_path = vuln['screenshots'][stage]
                html += f"""
                <div class="image-container">
                    <h4>{stage_names[stage]}</h4>
                    <p style="color: #6c757d; font-style: italic;">{stage_descriptions[stage]}</p>
                    <div class="path-info">المسار: {image_path}</div>
                    <img src="{convert_to_relative_path(image_path)}" alt="{stage_names[stage]}"
                         onload="this.nextElementSibling.style.display='block'; this.nextElementSibling.nextElementSibling.style.display='none'; console.log('✅ تم تحميل صورة {stage} بنجاح');"
                         onerror="this.style.display='none'; this.nextElementSibling.style.display='none'; this.nextElementSibling.nextElementSibling.style.display='block'; console.log('❌ فشل في تحميل صورة {stage}');">
                    <div class="success" style="display:none;">✅ تم تحميل الصورة بنجاح - تُظهر التأثيرات الحقيقية للثغرة</div>
                    <div class="error" style="display:none;">❌ فشل في تحميل الصورة: {image_path}</div>
                </div>
"""
        
        html += f"""
            </div>
            
            <div class="section">
                <h3>💡 التوصيات والإصلاحات</h3>
                <p style="font-size: 1.1em;">{vuln['recommendation']}</p>
            </div>
        </div>
"""
    
    html += """
    </div>
    
    <script>
        console.log('🔥 تم تحميل التقرير الرئيسي النهائي v4');
        
        let finalStats = { total: 0, loaded: 0, failed: 0 };
        
        document.querySelectorAll('img').forEach(img => {
            finalStats.total++;
            
            img.addEventListener('load', function() {
                finalStats.loaded++;
                console.log(`✅ تم تحميل صورة (${finalStats.loaded}/${finalStats.total})`);
                updateFinalStats();
            });
            
            img.addEventListener('error', function() {
                finalStats.failed++;
                console.log(`❌ فشل في تحميل صورة (${finalStats.failed}/${finalStats.total})`);
                updateFinalStats();
            });
        });
        
        function updateFinalStats() {
            if (finalStats.loaded + finalStats.failed === finalStats.total) {
                console.log(`📊 إحصائيات نهائية للتقرير الرئيسي:`);
                console.log(`   إجمالي: ${finalStats.total}`);
                console.log(`   نجحت: ${finalStats.loaded}`);
                console.log(`   فشلت: ${finalStats.failed}`);
                console.log(`   نسبة النجاح: ${((finalStats.loaded / finalStats.total) * 100).toFixed(1)}%`);
                
                if (finalStats.loaded === finalStats.total) {
                    console.log('🎉 جميع الصور تم تحميلها بنجاح! التقرير الرئيسي يعمل بشكل مثالي!');
                }
            }
        }
        
        console.log(`📸 إجمالي الصور في التقرير الرئيسي: ${finalStats.total}`);
    </script>
</body>
</html>
"""
    
    return html

def generate_final_separate_report(vulnerabilities, report_id):
    """إنشاء التقرير المنفصل النهائي"""
    
    html = f"""
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>📄 التقرير المنفصل النهائي v4 - الأدلة البصرية</title>
    <style>
        body {{ 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
            margin: 0; 
            padding: 20px; 
            background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
            min-height: 100vh;
        }}
        .container {{ 
            max-width: 1200px; 
            margin: 0 auto; 
            background: white; 
            padding: 40px; 
            border-radius: 15px; 
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }}
        .header {{
            text-align: center;
            background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
            color: white;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 30px;
        }}
        .vulnerability {{ 
            margin: 40px 0; 
            padding: 30px; 
            border: 3px solid #17a2b8; 
            border-radius: 15px; 
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }}
        .section {{ 
            margin: 25px 0; 
            padding: 25px; 
            background: #e9ecef; 
            border-radius: 12px; 
            border-left: 5px solid #17a2b8;
        }}
        .visual-changes {{ 
            background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%); 
            border: 3px solid #17a2b8; 
            border-radius: 15px;
            padding: 30px;
        }}
        .image-container {{ 
            margin: 25px 0; 
            padding: 25px; 
            border: 2px solid #17a2b8; 
            border-radius: 12px; 
            background: white;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }}
        .image-container img {{ 
            max-width: 100%; 
            height: auto; 
            border: 3px solid #17a2b8; 
            border-radius: 10px;
            box-shadow: 0 8px 20px rgba(0,0,0,0.2);
        }}
        .success {{ 
            color: #155724; 
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%); 
            padding: 15px; 
            border-radius: 8px; 
            margin: 15px 0; 
            border: 2px solid #28a745;
            font-weight: bold;
        }}
        .error {{ 
            color: #721c24; 
            background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%); 
            padding: 15px; 
            border-radius: 8px; 
            margin: 15px 0; 
            border: 2px solid #dc3545;
            font-weight: bold;
        }}
        .path-info {{ 
            font-family: 'Courier New', monospace; 
            background: #f8f9fa; 
            padding: 12px; 
            border-radius: 6px; 
            font-size: 11px; 
            color: #495057;
            border: 1px solid #dee2e6;
        }}
        h1 {{ color: white; font-size: 2.5em; margin: 0; }}
        h2 {{ color: #17a2b8; border-bottom: 3px solid #17a2b8; padding-bottom: 15px; font-size: 1.8em; }}
        h3 {{ color: #495057; font-size: 1.4em; }}
        h4 {{ color: #17a2b8; font-size: 1.2em; }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📄 التقرير المنفصل النهائي v4</h1>
            <p style="font-size: 1.2em; margin: 10px 0;">تقرير مفصل يحتوي على جميع الأدلة البصرية للثغرات المكتشفة</p>
            <p><strong>Report ID:</strong> {report_id}</p>
        </div>
"""

    for i, vuln in enumerate(vulnerabilities, 1):
        html += f"""
        <div class="vulnerability">
            <h2>📋 تفاصيل الثغرة {i}: {vuln['name']}</h2>
            
            <div class="section">
                <h3>ℹ️ معلومات الثغرة التفصيلية</h3>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                    <div>
                        <p><strong>النوع:</strong> <span style="color: #17a2b8;">{vuln['type']}</span></p>
                        <p><strong>مستوى الخطورة:</strong> <span style="color: {'#dc3545' if vuln['severity'] == 'Critical' else '#fd7e14'};">{vuln['severity']}</span></p>
                    </div>
                    <div>
                        <p><strong>الوصف التفصيلي:</strong> {vuln['description']}</p>
                    </div>
                </div>
            </div>
            
            <div class="section visual-changes">
                <h3>🖼️ الأدلة البصرية (Visual Proof)</h3>
                <p style="font-size: 1.1em; font-weight: bold;">الصور التالية تُظهر الدليل البصري الكامل على وجود الثغرة وتأثيرها الحقيقي:</p>
"""
        
        # إضافة الصور من visual_proof
        stages = ['before', 'during', 'after']
        stage_names = {
            'before': '🔍 الحالة الأصلية للموقع', 
            'during': '⚡ عملية الاستغلال الفعلية', 
            'after': '💥 نتيجة الاستغلال والتأثيرات'
        }
        stage_descriptions = {
            'before': 'حالة الموقع الطبيعية قبل أي تدخل أو استغلال',
            'during': 'الموقع أثناء تطبيق payload الثغرة وعملية الاستغلال',
            'after': 'النتيجة النهائية للاستغلال مع إظهار جميع التأثيرات والأضرار'
        }
        
        for stage in stages:
            key = f'{stage}_screenshot_path'
            if key in vuln['visual_proof']:
                image_path = vuln['visual_proof'][key]
                html += f"""
                <div class="image-container">
                    <h4>{stage_names[stage]}</h4>
                    <p style="color: #6c757d; font-style: italic; margin-bottom: 15px;">{stage_descriptions[stage]}</p>
                    <div class="path-info">مسار الدليل البصري: {image_path}</div>
                    <img src="{convert_to_relative_path(image_path)}" alt="{stage_names[stage]}"
                         onload="this.nextElementSibling.style.display='block'; this.nextElementSibling.nextElementSibling.style.display='none'; console.log('✅ تم تحميل دليل بصري {stage} بنجاح');"
                         onerror="this.style.display='none'; this.nextElementSibling.style.display='none'; this.nextElementSibling.nextElementSibling.style.display='block'; console.log('❌ فشل في تحميل دليل بصري {stage}');">
                    <div class="success" style="display:none;">✅ تم تحميل الدليل البصري بنجاح - يُظهر التأثيرات الحقيقية</div>
                    <div class="error" style="display:none;">❌ فشل في تحميل الدليل البصري: {image_path}</div>
                </div>
"""
        
        html += f"""
            </div>
            
            <div class="section">
                <h3>⚠️ تحليل التأثير المفصل</h3>
                <p style="font-size: 1.1em;">{vuln['impact']}</p>
            </div>
            
            <div class="section">
                <h3>🔧 خطوات الإصلاح والحماية</h3>
                <p style="font-size: 1.1em;">{vuln['recommendation']}</p>
            </div>
        </div>
"""
    
    html += """
    </div>
    
    <script>
        console.log('📄 تم تحميل التقرير المنفصل النهائي v4');
        
        let separateFinalStats = { total: 0, loaded: 0, failed: 0, byStage: { before: 0, during: 0, after: 0 } };
        
        document.querySelectorAll('img').forEach(img => {
            separateFinalStats.total++;
            
            // تحديد المرحلة من alt text
            const stage = img.alt.includes('الأصلية') ? 'before' : 
                         img.alt.includes('الاستغلال الفعلية') ? 'during' : 'after';
            
            img.addEventListener('load', function() {
                separateFinalStats.loaded++;
                separateFinalStats.byStage[stage]++;
                console.log(`✅ تم تحميل دليل بصري ${stage} (${separateFinalStats.loaded}/${separateFinalStats.total})`);
                updateSeparateFinalStats();
            });
            
            img.addEventListener('error', function() {
                separateFinalStats.failed++;
                console.log(`❌ فشل في تحميل دليل بصري ${stage} (${separateFinalStats.failed}/${separateFinalStats.total})`);
                updateSeparateFinalStats();
            });
        });
        
        function updateSeparateFinalStats() {
            if (separateFinalStats.loaded + separateFinalStats.failed === separateFinalStats.total) {
                console.log('📊 إحصائيات نهائية للتقرير المنفصل:');
                console.log(`   إجمالي الأدلة البصرية: ${separateFinalStats.total}`);
                console.log(`   تم تحميلها بنجاح: ${separateFinalStats.loaded}`);
                console.log(`   فشل في التحميل: ${separateFinalStats.failed}`);
                console.log(`   الحالة الأصلية: ${separateFinalStats.byStage.before}`);
                console.log(`   عملية الاستغلال: ${separateFinalStats.byStage.during}`);
                console.log(`   نتيجة الاستغلال: ${separateFinalStats.byStage.after}`);
                console.log(`   نسبة النجاح: ${((separateFinalStats.loaded / separateFinalStats.total) * 100).toFixed(1)}%`);
                
                if (separateFinalStats.loaded === separateFinalStats.total) {
                    console.log('🎉 جميع الأدلة البصرية تم تحميلها بنجاح! التقرير المنفصل يعمل بشكل مثالي!');
                }
            }
        }
        
        console.log(`🖼️ إجمالي الأدلة البصرية في التقرير المنفصل: ${separateFinalStats.total}`);
    </script>
</body>
</html>
"""
    
    return html

if __name__ == "__main__":
    create_final_reports_with_existing_images()
