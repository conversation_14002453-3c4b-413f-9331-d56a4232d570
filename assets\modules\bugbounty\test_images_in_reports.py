#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json
import time
import os
from pathlib import Path

def test_images_in_reports():
    """اختبار عرض الصور في التقارير"""
    
    print("🔥 اختبار عرض الصور في التقارير v4")
    print("🎯 إنشاء ثغرات مع صور وتوليد تقرير لفحص عرض الصور")
    print("=" * 70)
    
    # إنشاء ثغرات مع صور
    vulnerabilities = create_vulnerabilities_with_images()
    
    if vulnerabilities:
        print(f"✅ تم إنشاء {len(vulnerabilities)} ثغرة مع صور")
        
        # إنشاء تقرير لاختبار عرض الصور
        report_result = generate_test_report(vulnerabilities)
        
        if report_result:
            print("✅ تم إنشاء التقرير بنجاح")
            check_images_in_report(report_result)
        else:
            print("❌ فشل في إنشاء التقرير")
    else:
        print("❌ فشل في إنشاء الثغرات")

def create_vulnerabilities_with_images():
    """إنشاء ثغرات مع صور للاختبار"""
    
    print("\n📸 إنشاء ثغرات مع صور...")
    
    vulnerabilities = []
    
    # ثغرات للاختبار
    test_vulns = [
        {
            'name': 'XSS Cross Site Scripting',
            'type': 'XSS',
            'payload': '<script>alert("XSS_TEST")</script>',
            'param': 'searchFor'
        },
        {
            'name': 'SQL Injection Attack', 
            'type': 'SQL_Injection',
            'payload': "' UNION SELECT version() --",
            'param': 'id'
        }
    ]
    
    report_id = f"image_test_{int(time.time())}"
    
    for i, vuln in enumerate(test_vulns, 1):
        print(f"\n🎯 إنشاء صور للثغرة {i}: {vuln['name']}")
        
        # إنشاء الصور الثلاث
        vuln_with_images = create_images_for_vulnerability(vuln, report_id, i)
        
        if vuln_with_images:
            vulnerabilities.append(vuln_with_images)
            print(f"   ✅ تم إنشاء صور الثغرة {i}")
        else:
            print(f"   ❌ فشل في إنشاء صور الثغرة {i}")
    
    return vulnerabilities

def create_images_for_vulnerability(vuln, report_id, vuln_num):
    """إنشاء الصور الثلاث لثغرة واحدة"""
    
    stages = ['before', 'during', 'after']
    image_paths = {}
    
    for stage in stages:
        print(f"      📸 إنشاء صورة {stage}...")
        
        data = {
            'url': 'http://testphp.vulnweb.com/search.php',
            'filename': f'{stage}_{vuln["type"]}_IMAGE_TEST_{vuln_num}',
            'report_id': report_id,
            'vulnerability_name': vuln['name'],
            'vulnerability_type': vuln['type'],
            'stage': stage,
            'payload_data': vuln['payload'],
            'target_parameter': vuln['param']
        }
        
        try:
            response = requests.post(
                'http://localhost:8000/v4_website',
                json=data,
                timeout=45
            )
            
            if response.status_code == 200:
                result = response.json()
                
                if result.get('success'):
                    image_path = result.get('file_path', '')
                    size = result.get('file_size', 0)
                    
                    # تحويل المسار إلى مسار نسبي
                    if image_path:
                        relative_path = image_path.replace('E:\\المساعد ai\\مساعد 2\\', './')
                        image_paths[stage] = relative_path
                        print(f"         ✅ {size:,} bytes - {relative_path}")
                    else:
                        print(f"         ❌ لم يتم إرجاع مسار الصورة")
                else:
                    print(f"         ❌ فشل: {result.get('error', 'Unknown')}")
            else:
                print(f"         ❌ HTTP {response.status_code}")
                
        except Exception as e:
            print(f"         ❌ خطأ: {e}")
    
    # إنشاء بيانات الثغرة مع مسارات الصور
    if len(image_paths) >= 2:  # على الأقل صورتان
        vuln_with_images = {
            'name': vuln['name'],
            'type': vuln['type'],
            'severity': 'High',
            'description': f'Test vulnerability for image display: {vuln["name"]}',
            'screenshots': image_paths,
            'visual_proof': {
                f'{stage}_screenshot_path': path for stage, path in image_paths.items()
            }
        }
        
        return vuln_with_images
    
    return None

def generate_test_report(vulnerabilities):
    """إنشاء تقرير اختبار"""
    
    print(f"\n📋 إنشاء تقرير اختبار مع {len(vulnerabilities)} ثغرة...")
    
    try:
        # محاكاة إنشاء تقرير
        report_data = {
            'vulnerabilities': vulnerabilities,
            'website_url': 'http://testphp.vulnweb.com',
            'scan_date': time.strftime('%Y-%m-%d %H:%M:%S'),
            'total_vulnerabilities': len(vulnerabilities)
        }
        
        print("✅ تم إنشاء بيانات التقرير")
        return report_data
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء التقرير: {e}")
        return None

def check_images_in_report(report_data):
    """فحص مسارات الصور في التقرير"""
    
    print(f"\n🔍 فحص مسارات الصور في التقرير:")
    print("-" * 50)
    
    for i, vuln in enumerate(report_data['vulnerabilities'], 1):
        print(f"\n🎯 الثغرة {i}: {vuln['name']}")
        
        # فحص مسارات screenshots
        if 'screenshots' in vuln:
            print(f"   📸 مسارات Screenshots:")
            for stage, path in vuln['screenshots'].items():
                exists = check_file_exists(path)
                status = "✅ موجود" if exists else "❌ غير موجود"
                print(f"      {stage}: {path} - {status}")
        else:
            print(f"   ❌ لا توجد مسارات screenshots")
        
        # فحص مسارات visual_proof
        if 'visual_proof' in vuln:
            print(f"   🖼️ مسارات Visual Proof:")
            for key, path in vuln['visual_proof'].items():
                if key.endswith('_screenshot_path'):
                    stage = key.replace('_screenshot_path', '')
                    exists = check_file_exists(path)
                    status = "✅ موجود" if exists else "❌ غير موجود"
                    print(f"      {stage}: {path} - {status}")
        else:
            print(f"   ❌ لا توجد مسارات visual_proof")

def check_file_exists(file_path):
    """فحص وجود الملف"""
    
    try:
        # تحويل المسار النسبي إلى مسار مطلق
        if file_path.startswith('./'):
            abs_path = file_path.replace('./', '')
            full_path = Path(abs_path)
        else:
            full_path = Path(file_path)
        
        return full_path.exists()
        
    except Exception as e:
        return False

if __name__ == "__main__":
    test_images_in_reports()
