#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json
import time
import os
from pathlib import Path

def test_real_report_images():
    """اختبار حقيقي لعرض الصور في التقارير v4"""
    
    print("🔥 اختبار حقيقي لعرض الصور في التقارير v4")
    print("🎯 إنشاء ثغرات حقيقية مع صور وتوليد تقرير كامل")
    print("=" * 70)
    
    # 1. إنشاء ثغرات مع صور حقيقية
    vulnerabilities = create_real_vulnerabilities_with_images()
    
    if vulnerabilities:
        print(f"✅ تم إنشاء {len(vulnerabilities)} ثغرة مع صور")
        
        # 2. محاكاة النظام v4 لإنشاء التقرير
        simulate_v4_report_generation(vulnerabilities)
        
        # 3. فحص التقارير المُنشأة
        check_generated_reports()
    else:
        print("❌ فشل في إنشاء الثغرات")

def create_real_vulnerabilities_with_images():
    """إنشاء ثغرات حقيقية مع صور للاختبار"""
    
    print("\n📸 إنشاء ثغرات حقيقية مع صور...")
    
    vulnerabilities = []
    report_id = f"real_report_test_{int(time.time())}"
    
    # ثغرات حقيقية للاختبار
    test_vulns = [
        {
            'name': 'XSS Cross Site Scripting',
            'type': 'XSS',
            'payload': '<script>alert("XSS_REPORT_TEST")</script>',
            'param': 'searchFor',
            'severity': 'High'
        },
        {
            'name': 'SQL Injection Attack', 
            'type': 'SQL_Injection',
            'payload': "' UNION SELECT version(), database() --",
            'param': 'id',
            'severity': 'Critical'
        }
    ]
    
    for i, vuln in enumerate(test_vulns, 1):
        print(f"\n🎯 إنشاء ثغرة {i}: {vuln['name']}")
        
        # إنشاء الصور الثلاث للثغرة
        vuln_with_images = create_complete_vulnerability_with_images(vuln, report_id, i)
        
        if vuln_with_images:
            vulnerabilities.append(vuln_with_images)
            print(f"   ✅ تم إنشاء ثغرة {i} مع صور كاملة")
        else:
            print(f"   ❌ فشل في إنشاء ثغرة {i}")
    
    return vulnerabilities

def create_complete_vulnerability_with_images(vuln, report_id, vuln_num):
    """إنشاء ثغرة كاملة مع الصور الثلاث"""
    
    stages = ['before', 'during', 'after']
    image_paths = {}
    image_data = {}
    
    for stage in stages:
        print(f"      📸 إنشاء صورة {stage}...")
        
        data = {
            'url': 'http://testphp.vulnweb.com/search.php',
            'filename': f'{stage}_{vuln["type"]}_REPORT_TEST_{vuln_num}',
            'report_id': report_id,
            'vulnerability_name': vuln['name'],
            'vulnerability_type': vuln['type'],
            'stage': stage,
            'payload_data': vuln['payload'],
            'target_parameter': vuln['param']
        }
        
        try:
            response = requests.post(
                'http://localhost:8000/v4_website',
                json=data,
                timeout=45
            )
            
            if response.status_code == 200:
                result = response.json()
                
                if result.get('success'):
                    file_path = result.get('file_path', '')
                    size = result.get('file_size', 0)
                    
                    if file_path:
                        # تحويل إلى مسار نسبي
                        relative_path = file_path.replace('E:\\المساعد ai\\مساعد 2\\', './')
                        image_paths[stage] = relative_path
                        image_data[f'{stage}_screenshot_path'] = relative_path
                        
                        print(f"         ✅ {size:,} bytes - {relative_path}")
                    else:
                        print(f"         ❌ لم يتم إرجاع مسار الصورة")
                else:
                    print(f"         ❌ فشل: {result.get('error', 'Unknown')}")
            else:
                print(f"         ❌ HTTP {response.status_code}")
                
        except Exception as e:
            print(f"         ❌ خطأ: {e}")
    
    # إنشاء بيانات الثغرة الكاملة
    if len(image_paths) >= 2:
        vuln_complete = {
            'name': vuln['name'],
            'type': vuln['type'],
            'severity': vuln['severity'],
            'description': f'Real vulnerability test for report: {vuln["name"]}',
            'impact': f'High impact {vuln["type"]} vulnerability',
            'recommendation': f'Fix {vuln["type"]} vulnerability immediately',
            
            # مسارات الصور - طريقة 1
            'screenshots': image_paths,
            
            # مسارات الصور - طريقة 2  
            'visual_proof': image_data,
            
            # معلومات إضافية
            'payload': vuln['payload'],
            'parameter': vuln['param'],
            'report_id': report_id
        }
        
        return vuln_complete
    
    return None

def simulate_v4_report_generation(vulnerabilities):
    """محاكاة إنشاء التقرير بالنظام v4"""
    
    print(f"\n📋 محاكاة إنشاء التقرير بالنظام v4...")
    
    # محاكاة استدعاء النظام v4
    try:
        # إنشاء ملف HTML للتقرير
        report_html = generate_test_report_html(vulnerabilities)
        
        # حفظ التقرير
        report_path = f"test_report_{int(time.time())}.html"
        
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(report_html)
        
        print(f"✅ تم إنشاء تقرير اختبار: {report_path}")
        
        # فحص مسارات الصور في التقرير
        check_image_paths_in_report(report_html, vulnerabilities)
        
        return report_path
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء التقرير: {e}")
        return None

def generate_test_report_html(vulnerabilities):
    """إنشاء HTML للتقرير الاختبار"""
    
    html = """
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>تقرير اختبار الصور v4</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .vulnerability { border: 1px solid #ccc; margin: 20px 0; padding: 20px; }
        .images-section { margin: 20px 0; }
        .image-container { margin: 10px 0; }
        img { max-width: 300px; border: 1px solid #ddd; }
    </style>
</head>
<body>
    <h1>🔥 تقرير اختبار عرض الصور v4</h1>
    <p>تاريخ الإنشاء: """ + time.strftime('%Y-%m-%d %H:%M:%S') + """</p>
    
"""
    
    for i, vuln in enumerate(vulnerabilities, 1):
        html += f"""
    <div class="vulnerability">
        <h2>🎯 الثغرة {i}: {vuln['name']}</h2>
        <p><strong>النوع:</strong> {vuln['type']}</p>
        <p><strong>الخطورة:</strong> {vuln['severity']}</p>
        <p><strong>الوصف:</strong> {vuln['description']}</p>
        
        <div class="images-section">
            <h3>📸 الصور التوضيحية:</h3>
"""
        
        # إضافة الصور من screenshots
        if 'screenshots' in vuln:
            for stage, path in vuln['screenshots'].items():
                html += f"""
            <div class="image-container">
                <h4>{stage.title()} الاستغلال:</h4>
                <p>المسار: <code>{path}</code></p>
                <img src="{path}" alt="{stage} screenshot" onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                <p style="display:none; color:red;">❌ فشل في تحميل الصورة: {path}</p>
            </div>
"""
        
        # إضافة الصور من visual_proof
        if 'visual_proof' in vuln:
            html += """
            <h4>🖼️ Visual Proof Paths:</h4>
"""
            for key, path in vuln['visual_proof'].items():
                if key.endswith('_screenshot_path'):
                    stage = key.replace('_screenshot_path', '')
                    html += f"""
            <div class="image-container">
                <p><strong>{stage}:</strong> <code>{path}</code></p>
                <img src="{path}" alt="{stage} visual proof" onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                <p style="display:none; color:red;">❌ فشل في تحميل الصورة: {path}</p>
            </div>
"""
        
        html += """
        </div>
    </div>
"""
    
    html += """
</body>
</html>
"""
    
    return html

def check_image_paths_in_report(report_html, vulnerabilities):
    """فحص مسارات الصور في التقرير"""
    
    print(f"\n🔍 فحص مسارات الصور في التقرير:")
    print("-" * 50)
    
    for i, vuln in enumerate(vulnerabilities, 1):
        print(f"\n🎯 الثغرة {i}: {vuln['name']}")
        
        # فحص screenshots paths
        if 'screenshots' in vuln:
            print(f"   📸 Screenshots paths:")
            for stage, path in vuln['screenshots'].items():
                exists = check_file_exists(path)
                in_html = path in report_html
                print(f"      {stage}: {path}")
                print(f"         📁 الملف موجود: {'✅' if exists else '❌'}")
                print(f"         📄 في HTML: {'✅' if in_html else '❌'}")
        
        # فحص visual_proof paths  
        if 'visual_proof' in vuln:
            print(f"   🖼️ Visual proof paths:")
            for key, path in vuln['visual_proof'].items():
                if key.endswith('_screenshot_path'):
                    stage = key.replace('_screenshot_path', '')
                    exists = check_file_exists(path)
                    in_html = path in report_html
                    print(f"      {stage}: {path}")
                    print(f"         📁 الملف موجود: {'✅' if exists else '❌'}")
                    print(f"         📄 في HTML: {'✅' if in_html else '❌'}")

def check_file_exists(file_path):
    """فحص وجود الملف"""
    try:
        if file_path.startswith('./'):
            abs_path = file_path.replace('./', '')
            full_path = Path(abs_path)
        else:
            full_path = Path(file_path)
        
        return full_path.exists()
    except:
        return False

def check_generated_reports():
    """فحص التقارير المُنشأة"""
    
    print(f"\n📋 فحص التقارير المُنشأة:")
    print("-" * 40)
    
    # البحث عن ملفات التقارير
    report_files = list(Path('.').glob('test_report_*.html'))
    
    if report_files:
        print(f"✅ تم العثور على {len(report_files)} تقرير:")
        
        for report_file in report_files:
            size = report_file.stat().st_size
            print(f"   📄 {report_file.name} - {size:,} bytes")
            
            # فحص محتوى التقرير
            try:
                with open(report_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                img_tags = content.count('<img src=')
                error_messages = content.count('فشل في تحميل الصورة')
                
                print(f"      🖼️ عدد صور HTML: {img_tags}")
                print(f"      ❌ رسائل خطأ: {error_messages}")
                
            except Exception as e:
                print(f"      ❌ خطأ في قراءة التقرير: {e}")
    else:
        print(f"❌ لم يتم العثور على تقارير")

if __name__ == "__main__":
    test_real_report_images()
