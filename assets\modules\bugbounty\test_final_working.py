#!/usr/bin/env python3
"""
🔥 اختبار نهائي يعمل بالتأكيد
اختبار مباشر للدالة الأصلية بدون تعقيدات
"""

import requests
import json
import time

def test_server():
    """اختبار السيرفر مباشرة"""
    print("🔥 اختبار السيرفر مباشرة...")
    
    try:
        # البيانات للدالة الأصلية الشاملة v4_website
        payload = {
            "url": "https://httpbin.org/html",
            "vulnerability_name": "XSS_Real_Test",
            "vulnerability_type": "Cross-Site Scripting",
            "stage": "after",  # اختبار مرحلة بعد الاستغلال
            "payload_data": "<script>alert('🔥 XSS AFTER EXPLOITATION! 🔥'); document.body.style.background='red';</script>",
            "target_parameter": "test_param",
            "filename": "after_exploitation_test",
            "report_id": f"test_report_{int(time.time())}"
        }
        
        print("📤 إرسال طلب للدالة الأصلية...")
        print(f"📋 البيانات: {json.dumps(payload, indent=2)}")
        
        # استخدام الدالة الأصلية الشاملة v4_website
        response = requests.post(
            "http://localhost:8000/v4_website",
            json=payload,
            timeout=60
        )
        
        print(f"📥 رد السيرفر: {response.status_code}")
        print(f"📄 المحتوى: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ نجح الطلب!")
            print(f"📊 النتيجة: {json.dumps(result, indent=2, ensure_ascii=False)}")
        else:
            print(f"❌ فشل الطلب: {response.status_code}")
            
    except Exception as e:
        print(f"❌ خطأ: {e}")

if __name__ == "__main__":
    test_server()
