/**
 * اختبار شامل لكشف جميع مشاكل ملفات Python
 */

const fetch = globalThis.fetch;

async function testPythonIssues() {
    console.log('🔍 بدء اختبار شامل لكشف مشاكل Python...');
    
    const serverUrl = 'http://localhost:8000';
    
    try {
        // 1. اختبار health check
        console.log('\n1️⃣ اختبار health check...');
        const healthResponse = await fetch(`${serverUrl}/health`);
        if (healthResponse.ok) {
            const healthData = await healthResponse.json();
            console.log('✅ Health check نجح:', healthData);
        } else {
            console.error('❌ Health check فشل');
            return;
        }
        
        // 2. اختبار مع بيانات حقيقية كاملة
        console.log('\n2️⃣ اختبار مع بيانات حقيقية كاملة...');
        const realTestData = {
            url: 'http://testphp.vulnweb.com/search.php',
            filename: 'test_real_data',
            report_id: 'test_real_report',
            vulnerability_name: 'XSS_Cross_Site_Scripting',
            vulnerability_type: 'XSS',
            stage: 'before',
            payload_data: '<script>alert("REAL_XSS_TEST")</script>',
            target_parameter: 'searchFor'
        };
        
        console.log('📤 إرسال بيانات حقيقية:', realTestData);
        
        const realResponse = await fetch(`${serverUrl}/v4_website`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(realTestData)
        });
        
        if (realResponse.ok) {
            const realResult = await realResponse.json();
            console.log('📥 نتيجة البيانات الحقيقية:', {
                success: realResult.success,
                vulnerability_name: realResult.vulnerability_name,
                stage: realResult.stage,
                payload_used: realResult.payload_used,
                error: realResult.error
            });
        } else {
            console.error('❌ فشل اختبار البيانات الحقيقية:', realResponse.status);
        }
        
        // 3. اختبار الصور الثلاث متتالية
        console.log('\n3️⃣ اختبار الصور الثلاث متتالية...');
        const stages = ['before', 'during', 'after'];
        
        for (const stage of stages) {
            console.log(`\n   📸 اختبار مرحلة ${stage}...`);
            
            const stageData = {
                url: 'http://testphp.vulnweb.com/search.php',
                filename: `test_${stage}_stage`,
                report_id: 'test_stages_report',
                vulnerability_name: 'XSS_Cross_Site_Scripting',
                vulnerability_type: 'XSS',
                stage: stage,
                payload_data: stage === 'before' ? null : `<script>alert("${stage.toUpperCase()}_TEST")</script>`,
                target_parameter: 'searchFor'
            };
            
            console.log(`   📤 إرسال بيانات ${stage}:`, stageData);
            
            const stageResponse = await fetch(`${serverUrl}/v4_website`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(stageData)
            });
            
            if (stageResponse.ok) {
                const stageResult = await stageResponse.json();
                console.log(`   📥 نتيجة ${stage}:`, {
                    success: stageResult.success,
                    vulnerability_name: stageResult.vulnerability_name,
                    stage: stageResult.stage,
                    payload_used: stageResult.payload_used,
                    file_size: stageResult.file_size,
                    error: stageResult.error
                });
                
                if (!stageResult.success) {
                    console.error(`   ❌ فشل مرحلة ${stage}: ${stageResult.error}`);
                }
            } else {
                console.error(`   ❌ خطأ HTTP في مرحلة ${stage}: ${stageResponse.status}`);
            }
            
            // انتظار بين المراحل
            await new Promise(resolve => setTimeout(resolve, 3000));
        }
        
        // 4. اختبار مع ثغرات مختلفة
        console.log('\n4️⃣ اختبار مع ثغرات مختلفة...');
        const vulnerabilities = [
            {
                name: 'SQL_Injection',
                type: 'SQL',
                payload: "' OR '1'='1",
                parameter: 'id'
            },
            {
                name: 'Information_Disclosure',
                type: 'INFO',
                payload: null,
                parameter: null
            },
            {
                name: 'IDOR',
                type: 'IDOR',
                payload: '../../etc/passwd',
                parameter: 'file'
            }
        ];
        
        for (const vuln of vulnerabilities) {
            console.log(`\n   🎯 اختبار ثغرة ${vuln.name}...`);
            
            const vulnData = {
                url: 'http://testphp.vulnweb.com',
                filename: `test_${vuln.name}`,
                report_id: 'test_vulns_report',
                vulnerability_name: vuln.name,
                vulnerability_type: vuln.type,
                stage: 'during',
                payload_data: vuln.payload,
                target_parameter: vuln.parameter
            };
            
            const vulnResponse = await fetch(`${serverUrl}/v4_website`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(vulnData)
            });
            
            if (vulnResponse.ok) {
                const vulnResult = await vulnResponse.json();
                console.log(`   📥 نتيجة ${vuln.name}:`, {
                    success: vulnResult.success,
                    vulnerability_name: vulnResult.vulnerability_name,
                    payload_used: vulnResult.payload_used,
                    error: vulnResult.error
                });
            } else {
                console.error(`   ❌ فشل اختبار ${vuln.name}: ${vulnResponse.status}`);
            }
            
            await new Promise(resolve => setTimeout(resolve, 2000));
        }
        
        console.log('\n🎉 انتهى الاختبار الشامل!');
        
    } catch (error) {
        console.error('❌ خطأ في الاختبار الشامل:', error);
    }
}

// تشغيل الاختبار
testPythonIssues();
