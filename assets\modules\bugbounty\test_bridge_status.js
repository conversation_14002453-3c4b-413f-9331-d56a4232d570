/**
 * اختبار حالة Python Screenshot Bridge
 */

async function testBridgeStatus() {
    console.log('🔍 فحص حالة Python Screenshot Bridge...');
    
    try {
        // 1. التحقق من وجود الـ bridge
        if (typeof window.pythonScreenshotBridge === 'undefined') {
            console.error('❌ pythonScreenshotBridge غير موجود في window');
            return false;
        }
        
        const bridge = window.pythonScreenshotBridge;
        console.log('✅ pythonScreenshotBridge موجود:', {
            version: bridge.version,
            serviceName: bridge.serviceName,
            isAvailable: bridge.isAvailable,
            connectionType: bridge.connectionType,
            webServiceUrl: bridge.webServiceUrl
        });
        
        // 2. فحص الاتصال يدوياً
        console.log('🔄 إعادة فحص الاتصال...');
        await bridge.checkPythonAvailability();
        
        console.log('📊 حالة الـ bridge بعد الفحص:', {
            isAvailable: bridge.isAvailable,
            connectionType: bridge.connectionType,
            webServiceUrl: bridge.webServiceUrl
        });
        
        // 3. اختبار استدعاء مباشر
        if (bridge.isAvailable) {
            console.log('🧪 اختبار استدعاء مباشر...');
            
            const testResult = await window.callPythonScreenshotService(
                'v4_website',
                'http://testphp.vulnweb.com',
                'bridge_test_screenshot',
                'test_report_bridge',
                'XSS_Cross_Site_Scripting',
                'XSS',
                'before',
                '<script>alert("test")</script>',
                'searchFor'
            );
            
            console.log('📥 نتيجة الاستدعاء المباشر:', testResult);
            
            if (testResult && testResult.success) {
                console.log('🎉 الـ bridge يعمل بشكل مثالي!');
                return true;
            } else {
                console.error('❌ فشل الاستدعاء المباشر');
                return false;
            }
        } else {
            console.error('❌ الـ bridge غير متاح');
            return false;
        }
        
    } catch (error) {
        console.error('❌ خطأ في اختبار الـ bridge:', error);
        return false;
    }
}

// تشغيل الاختبار
testBridgeStatus().then(success => {
    if (success) {
        console.log('🎉 Python Screenshot Bridge يعمل بشكل صحيح!');
    } else {
        console.log('💥 هناك مشكلة في Python Screenshot Bridge!');
    }
}).catch(error => {
    console.error('💥 خطأ في تشغيل اختبار الـ bridge:', error);
});
