const fetch = globalThis.fetch;

async function testSimple() {
    try {
        const response = await fetch('http://localhost:8000/v4_website', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                url: 'http://testphp.vulnweb.com',
                filename: 'test_simple',
                report_id: 'test_123',
                vulnerability_name: 'XSS_Test',
                stage: 'before'
            })
        });
        
        const result = await response.json();
        console.log('Result:', result);
        
    } catch (error) {
        console.error('Error:', error);
    }
}

testSimple();
