#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json

def quick_test_sync():
    """اختبار سريع للإصلاح المتزامن"""
    
    data = {
        'url': 'http://testphp.vulnweb.com/search.php',
        'filename': 'test_sync_fix',
        'report_id': 'sync_test',
        'vulnerability_name': 'XSS Cross Site Scripting',
        'vulnerability_type': 'XSS',
        'stage': 'before',
        'payload_data': '<script>alert("SYNC_TEST")</script>',
        'target_parameter': 'searchFor'
    }

    print('🧪 اختبار الإصلاح المتزامن...')

    try:
        response = requests.post('http://localhost:8000/v4_website', json=data, timeout=30)
        result = response.json()
        
        print(f'📊 الاستجابة: {response.status_code}')
        print(f'✅ النجاح: {result.get("success", False)}')
        print(f'📁 المسار: {result.get("file_path", "غير موجود")}')
        print(f'📏 الحجم: {result.get("file_size", 0)} bytes')
        
        if result.get('error'):
            print(f'❌ الخطأ: {result["error"]}')
        
        if result.get('success'):
            print('🎉 الإصلاح نجح!')
        else:
            print('❌ الإصلاح لم ينجح')
            
    except Exception as e:
        print(f'❌ خطأ في الطلب: {e}')

if __name__ == "__main__":
    quick_test_sync()
