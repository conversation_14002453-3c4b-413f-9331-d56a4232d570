/**
 * اختبار الاتصال بسيرفر Python
 */

async function testPythonServerConnection() {
    console.log('🔍 بدء اختبار الاتصال بسيرفر Python...');
    
    const serverUrl = 'http://localhost:8000';
    
    try {
        // 1. اختبار health check
        console.log('1️⃣ اختبار health check...');
        const healthResponse = await fetch(`${serverUrl}/health`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json'
            }
        });
        
        if (healthResponse.ok) {
            const healthData = await healthResponse.json();
            console.log('✅ Health check نجح:', healthData);
        } else {
            console.error('❌ Health check فشل:', healthResponse.status, healthResponse.statusText);
            return false;
        }
        
        // 2. اختبار endpoint الرئيسي
        console.log('2️⃣ اختبار endpoint /v4_website...');
        const testData = {
            url: 'http://testphp.vulnweb.com',
            filename: 'test_screenshot',
            report_id: 'test_report_123',
            vulnerability_name: 'XSS_Cross_Site_Scripting',
            vulnerability_type: 'XSS',
            stage: 'before',
            payload_data: '<script>alert("test")</script>',
            target_parameter: 'searchFor'
        };
        
        console.log('📤 إرسال بيانات الاختبار:', testData);
        
        const testResponse = await fetch(`${serverUrl}/v4_website`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(testData)
        });
        
        console.log('📥 استجابة السيرفر:', {
            status: testResponse.status,
            statusText: testResponse.statusText,
            ok: testResponse.ok
        });
        
        if (testResponse.ok) {
            const responseData = await testResponse.json();
            console.log('✅ اختبار /v4_website نجح:', responseData);
            return true;
        } else {
            const errorText = await testResponse.text();
            console.error('❌ اختبار /v4_website فشل:', errorText);
            return false;
        }
        
    } catch (error) {
        console.error('❌ خطأ في الاتصال:', error);
        return false;
    }
}

// تشغيل الاختبار
testPythonServerConnection().then(success => {
    if (success) {
        console.log('🎉 جميع الاختبارات نجحت! السيرفر يعمل بشكل صحيح.');
    } else {
        console.log('💥 فشل في الاختبارات! هناك مشكلة في السيرفر.');
    }
}).catch(error => {
    console.error('💥 خطأ في تشغيل الاختبار:', error);
});
