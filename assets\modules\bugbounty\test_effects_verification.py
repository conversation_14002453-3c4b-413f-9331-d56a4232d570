#!/usr/bin/env python3
"""
🔥 اختبار التحقق من التأثيرات الحقيقية في الصور
"""

import json
import time
import requests
import os
from PIL import Image
import hashlib

def test_single_stage(stage, vuln_name="XSS_Effects_Test"):
    """اختبار مرحلة واحدة والتحقق من الصورة"""
    
    payload_data = {
        "url": "https://httpbin.org/html",
        "vulnerability_name": vuln_name,
        "vulnerability_type": "Cross-Site Scripting",
        "stage": stage,
        "payload_data": f"<script>alert('🔥 {stage.upper()} STAGE TEST! 🔥'); document.body.style.background='red';</script>",
        "target_parameter": "test_param",
        "filename": f"{stage}_effects_test",
        "report_id": f"effects_test_{stage}_{int(time.time())}"
    }
    
    print(f"\n🔥 اختبار مرحلة {stage.upper()}")
    print("=" * 50)
    print(f"📤 إرسال طلب...")
    
    try:
        response = requests.post(
            "http://localhost:8000/v4_website",
            json=payload_data,
            timeout=60
        )
        
        print(f"📥 رد السيرفر: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ نجح الطلب: {result.get('message', 'تم بنجاح')}")
            
            # البحث عن الصورة
            screenshot_path = result.get('screenshot_path')
            if screenshot_path:
                print(f"📁 مسار الصورة: {screenshot_path}")
                
                # التحقق من وجود الصورة
                if os.path.exists(screenshot_path):
                    # فحص الصورة
                    img = Image.open(screenshot_path)
                    print(f"📏 حجم الصورة: {img.size}")
                    print(f"📊 حجم الملف: {os.path.getsize(screenshot_path)} bytes")
                    
                    # حساب hash للصورة
                    with open(screenshot_path, 'rb') as f:
                        img_hash = hashlib.md5(f.read()).hexdigest()
                    print(f"🔑 Hash الصورة: {img_hash}")
                    
                    return {
                        "success": True,
                        "path": screenshot_path,
                        "size": img.size,
                        "file_size": os.path.getsize(screenshot_path),
                        "hash": img_hash
                    }
                else:
                    print(f"❌ الصورة غير موجودة: {screenshot_path}")
                    return {"success": False, "error": "صورة غير موجودة"}
            else:
                print("❌ لا يوجد مسار صورة في الرد")
                return {"success": False, "error": "لا يوجد مسار صورة"}
        else:
            error_text = response.text
            print(f"❌ فشل الطلب: {response.status_code}")
            print(f"📄 خطأ: {error_text}")
            return {"success": False, "error": error_text}
            
    except Exception as e:
        print(f"❌ خطأ في الطلب: {e}")
        return {"success": False, "error": str(e)}

def compare_images(results):
    """مقارنة الصور للتحقق من التأثيرات"""
    print(f"\n🔍 مقارنة الصور للتحقق من التأثيرات:")
    print("=" * 50)
    
    stages = ['before', 'during', 'after']
    hashes = {}
    
    for stage in stages:
        if stage in results and results[stage].get('success'):
            hashes[stage] = results[stage]['hash']
            print(f"📋 {stage}: {results[stage]['hash']}")
        else:
            print(f"❌ {stage}: فشل")
    
    # التحقق من الاختلافات
    if len(hashes) >= 2:
        unique_hashes = set(hashes.values())
        if len(unique_hashes) == 1:
            print("⚠️ جميع الصور متطابقة - لا توجد تأثيرات!")
            return False
        else:
            print(f"✅ توجد {len(unique_hashes)} صور مختلفة - التأثيرات تعمل!")
            return True
    else:
        print("❌ عدد قليل من الصور للمقارنة")
        return False

def main():
    """الاختبار الرئيسي"""
    print("🔥🔥🔥 اختبار التحقق من التأثيرات الحقيقية 🔥🔥🔥")
    
    stages = ['before', 'during', 'after']
    results = {}
    
    for stage in stages:
        results[stage] = test_single_stage(stage)
        time.sleep(3)  # انتظار بين المراحل
    
    # مقارنة النتائج
    effects_working = compare_images(results)
    
    print(f"\n🎯 النتيجة النهائية:")
    print("=" * 50)
    if effects_working:
        print("✅ التأثيرات تعمل بشكل صحيح!")
    else:
        print("❌ التأثيرات لا تعمل - جميع الصور متطابقة!")
    
    # طباعة تفاصيل كل مرحلة
    for stage, result in results.items():
        status = "✅" if result.get('success') else "❌"
        print(f"{status} {stage}: {result.get('error', 'نجح')}")

if __name__ == "__main__":
    main()
